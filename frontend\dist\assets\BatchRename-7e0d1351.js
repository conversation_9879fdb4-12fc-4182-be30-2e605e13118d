import{u as Ml,a as te,f as Zl,_ as Nl}from"./index-4cbb9fd4.js";/* empty css                  *//* empty css                 *//* empty css               *//* empty css                    */import{x as ql,r,c as S,k as Wl,z as f,A as t,Q as l,I as s,M as v,O as g,L as C,P as Ee,a4 as Te,C as He,u as o,y as m,H as se,V as Ke,K as Gl}from"./vendor-a56c79b3.js";import{E as _,w as Qe,a as Jl,q as Hl,a9 as Kl,d as Ql,o as Xl,aa as Yl,A as et,x as lt,ab as tt,B as st,F as Se,ac as M,$ as ge,V,K as at,H as $,L as nt,M as we,N as ae,ad as be,ae as ot,af as it,f as dt,_ as Xe,h as Ye,ag as ut,ah as el,ai as rt,W as ne,a0 as ct,aj as vt,ak as pt,s as ll,al as he,a1 as oe,a2 as Re,a8 as _t,T as tl,a3 as sl,I as mt,J as ft,j as gt,k as wt,a5 as bt,a7 as ht,am as xt,an as yt,ao as kt,ap as Ct}from"./elementPlus-b5b9032b.js";const Vt={class:"batch-rename-page"},$t={class:"rename-banner"},zt={class:"banner-content"},It={class:"banner-text"},Ut={class:"banner-actions"},Et={class:"banner-visual"},Tt={class:"rename-icon"},St={class:"stats-section"},Rt={class:"section-title"},Pt={class:"stats-grid"},Bt={class:"stats-card primary"},Lt={class:"stats-icon"},Ft={class:"stats-content"},jt={class:"stats-value"},Dt={class:"stats-trend"},Ot={class:"stats-card success"},At={class:"stats-icon"},Mt={class:"stats-content"},Zt={class:"stats-value"},Nt={class:"stats-trend"},qt={class:"stats-card warning"},Wt={class:"stats-icon"},Gt={class:"stats-content"},Jt={class:"stats-value"},Ht={class:"stats-trend"},Kt={key:0,class:"performance-tip"},Qt={class:"stats-card info"},Xt={class:"stats-icon"},Yt={class:"stats-content"},es={class:"stats-value"},ls={class:"stats-trend"},ts={class:"main-workspace"},ss={class:"workspace-left"},as={class:"section-card"},ns={class:"section-header"},os={class:"section-title"},is={class:"section-actions"},ds={class:"section-content"},us={key:0,class:"file-toolbar"},rs={class:"file-stats"},cs={class:"stat-item"},vs={key:0,class:"stat-item"},ps={class:"stat-item"},_s={class:"file-search"},ms={key:1,class:"empty-state"},fs={class:"empty-illustration"},gs={class:"empty-icon"},ws={class:"empty-content"},bs={key:2,class:"file-list-container"},hs={class:"simple-file-table"},xs={class:"table-header"},ys={class:"header-cell checkbox-cell"},ks={class:"table-body"},Cs=["onClick"],Vs={class:"table-cell checkbox-cell"},$s={class:"table-cell name-cell"},zs={class:"file-info"},Is={class:"file-name"},Us={class:"table-cell size-cell"},Es={class:"file-size"},Ts={class:"table-cell index-cell"},Ss={class:"file-index"},Rs={class:"table-cell action-cell"},Ps={key:0,class:"pagination-wrapper"},Bs={class:"workspace-right"},Ls={class:"section-card"},Fs={class:"section-header"},js={class:"section-title"},Ds={class:"section-actions"},Os={class:"section-content"},As={class:"rule-form-modern"},Ms={class:"form-group-modern"},Zs={class:"form-label-modern"},Ns={key:0,class:"rule-config-modern"},qs={class:"form-group-modern"},Ws={class:"form-label-modern"},Gs={class:"form-group-modern"},Js={class:"form-label-modern"},Hs={key:1,class:"rule-config-modern"},Ks={class:"form-group-modern"},Qs={class:"form-label-modern"},Xs={key:2,class:"rule-config-modern"},Ys={class:"form-group-modern"},ea={class:"form-label-modern"},la={key:3,class:"rule-config-modern"},ta={class:"form-group-modern"},sa={class:"form-label-modern"},aa={class:"form-group-modern"},na={class:"form-label-modern"},oa={key:4,class:"rule-config-modern"},ia={class:"form-group-modern"},da={class:"form-label-modern"},ua={class:"form-group-modern"},ra={class:"form-label-modern"},ca={key:0,class:"mapping-status"},va={class:"status-item"},pa={class:"status-item"},_a={key:0},ma={key:1},fa={key:5,class:"rule-config-modern"},ga={class:"form-row-modern"},wa={class:"form-group-modern"},ba={class:"form-label-modern"},ha={class:"form-group-modern"},xa={class:"form-label-modern"},ya={class:"form-group-modern"},ka={class:"form-label-modern"},Ca={key:6,class:"rule-config-modern"},Va={class:"form-group-modern"},$a={class:"form-label-modern"},za={class:"form-actions"},Ia={class:"action-buttons-grid"},Ua={key:0,class:"btn-badge"},Ea={key:7,class:"progress-section"},Ta={class:"progress-header"},Sa={class:"progress-stats"},Ra={class:"progress-details"},Pa={class:"success-count"},Ba={key:0,class:"error-count"},La={class:"remaining-count"},Fa={key:0,class:"preview-section"},ja={class:"section-header"},Da={class:"section-title"},Oa={class:"preview-content"},Aa={class:"preview-list"},Ma={class:"preview-original"},Za={class:"preview-arrow"},Na={class:"preview-new"},qa={class:"upload-dialog-content"},Wa={class:"upload-area"},Ga={class:"upload-content"},Ja={class:"upload-tips"},Ha={class:"tip-item"},Ka={class:"tip-item"},Qa={class:"tip-item"},Xa={class:"export-dialog-content"},Ya={class:"export-info"},en={class:"export-text"},ln={class:"export-options"},tn={class:"dialog-footer"},sn={class:"batch-import-dialog-content"},an={class:"import-info"},nn={class:"import-methods"},on={class:"text-import"},dn={class:"import-tips"},un={class:"tip-item"},rn={class:"tip-item"},cn={class:"file-import"},vn={class:"upload-tips"},pn={class:"tip-item"},_n={class:"tip-item"},mn={key:0,class:"preview-section"},fn={class:"preview-list"},gn={class:"preview-original"},wn={class:"preview-arrow"},bn={class:"preview-new"},hn={key:0,class:"preview-more"},xn={class:"dialog-footer"},yn=ql({__name:"BatchRename",setup(kn){const xe=Ml(),x=r([]),u=r([]),ie=r(!1),Z=r(!1),al=r([]),E=r([]),N=r(!1),de=r(!1),q=r(!1),ye=r("zip"),R=r(!1),P=r(""),Pe=r("text"),y=r([]);r(10);const nl=r(0),ol=r(!1),ue=r(!1),ke=r(0),Be=r(0),re=r(0),W=r(!1),B=r(1),L=r(50),ce=r(""),Ce=r(null),k=r("regex"),G=r(""),J=r(""),D=r(""),H=r(""),K=r(""),ve=r(""),Q=r("文件"),pe=r(1),_e=r(3),X=r(""),z=r(""),I=r(""),Ve=r(!1),il=r(0),dl=r(50),Le=r("auto"),me=r(0),Fe=r(0),$e=r(null),ul=S(()=>x.value.length),rl=S(()=>E.value.length),cl=S(()=>"/api/v1/files/upload"),vl=S(()=>({Authorization:`Bearer ${xe.token}`})),Y=S(()=>ce.value.trim()?x.value.filter(n=>n.original_name.toLowerCase().includes(ce.value.toLowerCase())):x.value),pl=S(()=>{const n=Y.value.length,e=[20,50,100];if(n<=100)return e;if(n<=500)return[...e,200,500];if(n<=1e3)return[...e,200,500,1e3,n];if(n<=5e3)return[...e,200,500,1e3,2e3,Math.ceil(n/2),n];{const a=Math.ceil(n/4),d=Math.ceil(n/2);return[...e,200,500,1e3,2e3,a,d,n]}}),O=S(()=>{const n=(B.value-1)*L.value,e=n+L.value;return Y.value.slice(n,e)}),je=n=>{const e=n.lastIndexOf(".");return e>0?n.substring(e+1).toLowerCase():""},ze=n=>{const e=n.lastIndexOf(".");return e>0?n.substring(0,e):n},_l=n=>{const e=(n==null?void 0:n.toLowerCase())||"";return["jpg","jpeg","png","gif","webp","svg"].includes(e)?mt:["mp4","avi","mov","mkv","webm"].includes(e)?ft:$},U=n=>!n||!n.trim()?[]:n.trim().split(/[\n\s,]+/).map(e=>e.trim()).filter(e=>e.length>0),fe=async()=>{try{ie.value=!0;const n=await te.files.list({limit:1e3});x.value=n.data.data.files}catch(n){console.error("加载文件列表失败:",n),_.error("加载文件列表失败")}finally{ie.value=!1}},ml=()=>{u.value=[...x.value],h()},De=()=>{u.value=[],E.value=[]},Oe=n=>{const e=u.value.findIndex(a=>a.id===n.id);e>-1?u.value.splice(e,1):u.value.push(n),h()},fl=(n,e)=>{const a=je(n),d=n.substring(0,n.lastIndexOf("."))||n;switch(k.value){case"regex":if(G.value&&J.value)try{const p=new RegExp(G.value,"g"),w=d.replace(p,J.value);return a?`${w}.${a}`:w}catch{return n}return n;case"prefix":return a?`${D.value}${d}.${a}`:`${D.value}${d}`;case"suffix":return a?`${d}${H.value}.${a}`:`${d}${H.value}`;case"replace":if(K.value){const p=d.replace(new RegExp(K.value,"g"),ve.value);return a?`${p}.${a}`:p}return n;case"mapping":if(z.value&&I.value){const p=U(z.value),w=U(I.value);if(p.length!==w.length)return n;const T=new Map;p.forEach((F,ee)=>{T.set(F,w[ee])});let b=d;for(const[F,ee]of T)if(b.includes(F)){b=b.replace(new RegExp(F,"g"),ee);break}return a?`${b}.${a}`:b}return n;case"sequence":const c=(pe.value+e).toString().padStart(_e.value,"0");return a?`${Q.value}${c}.${a}`:`${Q.value}${c}`;case"extension":if(X.value.trim()){const p=X.value.trim().replace(/^\.+/,"");return p?`${d}.${p}`:d}return n;default:return n}},A=r(null),gl=S(()=>!Ve.value||u.value.length<=100?u.value:u.value.slice(il.value,dl.value)),Ae=()=>{A.value&&clearTimeout(A.value),A.value=window.setTimeout(()=>{if(u.value.length===0){E.value=[];return}Fe.value=performance.now(),Ve.value=u.value.length>100;const n=u.value.length>200?50:100,e=Ve.value?gl.value.slice(0,n):u.value.slice(0,n);requestAnimationFrame(()=>{E.value=e.map((a,d)=>({original:a.original_name,new:fl(a.original_name,d)})),me.value=performance.now()-Fe.value,me.value>100&&Le.value==="auto"&&(Le.value="high",_.warning(`检测到渲染较慢(${me.value.toFixed(1)}ms)，已自动启用高性能模式`))}),u.value.length>n&&_.info(`预览显示前${n}个文件，共选中${u.value.length}个文件`)},300)},wl=()=>{Ce.value&&clearTimeout(Ce.value),Ce.value=window.setTimeout(()=>{Pl()},300)},h=()=>{A.value&&clearTimeout(A.value),A.value=window.setTimeout(()=>{Ae()},300)},Me=()=>{$e.value&&clearTimeout($e.value),$e.value=window.setTimeout(()=>{Ae()},500)},Ze=()=>{h(),E.value.length===0&&_.warning("请先选择文件并配置重命名规则")},bl=async()=>{if(u.value.length===0){_.warning("请先选择要重命名的文件");return}try{await Qe.confirm(`确定要下载重命名后的 ${u.value.length} 个文件吗？`,"确认下载",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),Z.value=!0,ue.value=!0;try{const n={file_ids:u.value.map(a=>a.id),type:k.value,pattern:G.value,replacement:J.value,prefix:D.value,suffix:H.value,find_text:K.value,replace_text:ve.value,base_name:Q.value,start_number:pe.value,number_padding:_e.value,new_extension:X.value,mapping_from:z.value,mapping_to:I.value};console.log("DEBUG: Sending rename request:",n);const e=await fetch("/api/v1/rename/download",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${xe.token}`},body:JSON.stringify(n)});if(e.ok){const a=await e.json();if(a.success){E.value=a.data.previews.map(c=>({original:c.original_name,new:c.new_name}));const d={};a.data.previews.forEach((c,p)=>{var T;const w=(T=u.value[p])==null?void 0:T.id;w&&c.new_name&&(d[w]=c.new_name)}),await zl(d),_.success(`成功生成 ${a.data.total_count} 个重命名文件的下载`)}else _.error(a.message||"生成重命名文件失败")}else _.error("请求失败，请重试")}catch(n){console.error("下载重命名文件失败:",n),_.error("下载重命名文件失败")}}catch(n){n!=="cancel"&&(console.error("批量重命名失败:",n),_.error("批量重命名失败"))}finally{Z.value=!1,ue.value=!1}},hl=()=>{k.value="prefix",D.value=new Date().toISOString().split("T")[0]+"_",h(),_.success("已应用日期前缀模板")},xl=n=>n.size>104857600?(_.error("文件大小不能超过100MB"),!1):!0,yl=()=>{_.success("文件上传成功"),N.value=!1,fe()},kl=()=>{_.error("文件上传失败")},Cl=n=>{console.log("上传进度:",n.percent)},Vl=async()=>{try{if(R.value=!0,ye.value==="individual"){for(const n of u.value)try{const e=await te.files.download(n.id),a=new Blob([e.data]),d=window.URL.createObjectURL(a),c=document.createElement("a");c.href=d,c.download=n.original_name,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(d)}catch(e){console.error(`下载文件 ${n.original_name} 失败:`,e)}_.success("文件导出完成")}else try{const n=u.value.map(p=>p.id),e=await te.files.downloadZip(n),a=new Blob([e.data],{type:"application/zip"}),d=window.URL.createObjectURL(a),c=document.createElement("a");c.href=d,c.download=`批量重命名文件_${new Date().toISOString().split("T")[0]}.zip`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(d),_.success("ZIP文件下载完成")}catch(n){console.error("ZIP下载失败:",n),_.error("ZIP下载失败，请重试")}de.value=!1,De()}catch(n){console.error("导出文件失败:",n),_.error("导出文件失败")}finally{R.value=!1}},$l=async()=>{if(u.value.length===0){_.warning("请先选择要下载的文件");return}try{R.value=!0;const n=u.value.map(p=>p.id),e=await te.files.downloadZip(n),a=new Blob([e.data],{type:"application/zip"}),d=window.URL.createObjectURL(a),c=document.createElement("a");c.href=d,c.download=`选中文件_${new Date().toISOString().split("T")[0]}.zip`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(d),_.success("ZIP文件下载完成")}catch(n){console.error("ZIP下载失败:",n),_.error("ZIP下载失败，请重试")}finally{R.value=!1}},zl=async n=>{if(u.value.length===0){_.warning("请先选择要下载的文件");return}try{R.value=!0;const e=u.value.map(w=>w.id);console.log("DEBUG: downloadSelectedAsZipWithRename called with:",{fileIds:e,fileNameMapping:n,selectedFiles:u.value.map(w=>({id:w.id,name:w.original_name}))});const a=await fetch("/api/v1/files/download-zip",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${xe.token}`},body:JSON.stringify({file_ids:e,file_names:n})});if(!a.ok)throw new Error("下载请求失败");const d=await a.blob(),c=window.URL.createObjectURL(d),p=document.createElement("a");p.href=c,p.download=`重命名文件_${new Date().toISOString().split("T")[0]}.zip`,document.body.appendChild(p),p.click(),document.body.removeChild(p),window.URL.revokeObjectURL(c),_.success("重命名文件ZIP下载完成")}catch(e){console.error("重命名ZIP下载失败:",e),_.error("重命名ZIP下载失败，请重试")}finally{R.value=!1}},Il=n=>{const e=new FileReader;e.onload=a=>{var c;const d=(c=a.target)==null?void 0:c.result;P.value=d,Ne()},e.readAsText(n.raw)},Ne=()=>{if(!P.value.trim()){y.value=[];return}const n=P.value.trim().split(`
`),e=[];for(const a of n){const d=a.trim();if(!d)continue;const c=d.split(",");if(c.length>=2){const p=c[0].trim(),w=c.slice(1).join(",").trim();p&&w&&e.push({original:p,new:w})}}y.value=e,e.length>0?_.success(`成功解析 ${e.length} 条重命名规则`):_.warning("未找到有效的重命名规则")},Ul=async()=>{if(y.value.length===0){_.warning("没有可应用的重命名规则");return}try{await Qe.confirm(`确定要应用 ${y.value.length} 条重命名规则吗？`,"确认批量重命名",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),Z.value=!0;let n=0,e=0;for(const a of y.value){const d=x.value.find(c=>c.original_name===a.original);if(d)try{await te.files.update(d.id,{new_name:a.new}),n++}catch(c){console.error(`重命名文件 ${a.original} 失败:`,c),e++}else e++,console.warn(`未找到文件: ${a.original}`)}n>0?(_.success(`成功重命名 ${n} 个文件${e>0?`，${e} 个失败`:""}`),await fe(),q.value=!1,P.value="",y.value=[]):_.error("没有文件被重命名，请检查文件名是否匹配")}catch(n){n!=="cancel"&&(console.error("批量重命名失败:",n),_.error("批量重命名失败"))}finally{Z.value=!1}},El=async n=>{try{await navigator.clipboard.writeText(n),_.success("文件名已复制到剪贴板")}catch(e){console.error("复制失败:",e),_.error("复制失败，请手动复制")}},Tl=async n=>{try{const e=ze(n);await navigator.clipboard.writeText(e),_.success("文件名（无后缀）已复制到剪贴板")}catch(e){console.error("复制失败:",e),_.error("复制失败，请手动复制")}},Sl=n=>{L.value=n,B.value=1},Rl=n=>{B.value=n},Pl=()=>{B.value=1},Bl=async n=>{try{let e=[];switch(n){case"all-with-ext":e=x.value.map(p=>p.original_name);break;case"all-without-ext":e=x.value.map(p=>ze(p.original_name));break;case"selected-with-ext":e=u.value.map(p=>p.original_name);break;case"selected-without-ext":e=u.value.map(p=>ze(p.original_name));break;default:return}if(e.length===0){_.warning("没有文件可复制");return}const a=e.join(`
`);await navigator.clipboard.writeText(a);const d=n.includes("selected")?"选中":"所有",c=n.includes("without")?"（无后缀）":"（含后缀）";_.success(`已复制 ${e.length} 个${d}文件名${c}到剪贴板`)}catch(e){console.error("批量复制失败:",e),_.error("批量复制失败，请重试")}},Ll=()=>{wl()},Fl=()=>{W.value?(u.value=[],W.value=!1,_.info("已收缩所有文件选择")):(u.value=[...x.value],W.value=!0,_.success(`已展开选择所有 ${x.value.length} 个文件`)),h()},jl=n=>{n?u.value=[...O.value]:u.value=[],h()};return Wl(()=>{fe()}),(n,e)=>{const a=Jl,d=Hl,c=Kl,p=gt,w=wt,T=Ql,b=Xl,F=Yl,ee=et,j=bt,Dl=lt,qe=tt,We=ht,Ie=st,Ge=xt,Ol=yt,Je=kt,Al=Ct;return m(),f("div",Vt,[t("div",$t,[e[35]||(e[35]=t("div",{class:"banner-waves"},[t("div",{class:"wave wave-1"}),t("div",{class:"wave wave-2"}),t("div",{class:"wave wave-3"})],-1)),t("div",zt,[t("div",It,[e[33]||(e[33]=t("h1",{class:"banner-title"},"批量重命名工具",-1)),e[34]||(e[34]=t("p",{class:"banner-subtitle"}," 智能批量重命名工具，支持多种重命名规则和实时预览 ",-1)),t("div",Ut,[l(d,{type:"primary",size:"large",onClick:e[0]||(e[0]=i=>N.value=!0)},{default:s(()=>[l(a,null,{default:s(()=>[l(o(Se))]),_:1}),e[30]||(e[30]=v(" 上传文件 ",-1))]),_:1,__:[30]}),l(d,{size:"large",onClick:Ze,disabled:u.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[l(o(M))]),_:1}),e[31]||(e[31]=v(" 预览效果 ",-1))]),_:1,__:[31]},8,["disabled"]),l(d,{size:"large",onClick:$l,disabled:u.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[l(o(ge))]),_:1}),e[32]||(e[32]=v(" ZIP下载 ",-1))]),_:1,__:[32]},8,["disabled"])])]),t("div",Et,[t("div",Tt,[l(a,null,{default:s(()=>[l(o(V))]),_:1})])])])]),t("div",St,[t("h2",Rt,[l(a,null,{default:s(()=>[l(o(at))]),_:1}),e[36]||(e[36]=v(" 重命名统计 ",-1))]),t("div",Pt,[t("div",Bt,[e[39]||(e[39]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",Lt,[l(a,null,{default:s(()=>[l(o($))]),_:1})]),t("div",Ft,[t("div",jt,g(ul.value),1),e[38]||(e[38]=t("div",{class:"stats-label"},"总文件数",-1)),t("div",Dt,[l(a,null,{default:s(()=>[l(o(nt))]),_:1}),e[37]||(e[37]=t("span",null,"可重命名文件",-1))])])]),t("div",Ot,[e[42]||(e[42]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",At,[l(a,null,{default:s(()=>[l(o(we))]),_:1})]),t("div",Mt,[t("div",Zt,g(u.value.length),1),e[41]||(e[41]=t("div",{class:"stats-label"},"已选择",-1)),t("div",Nt,[l(a,null,{default:s(()=>[l(o(ae))]),_:1}),e[40]||(e[40]=t("span",null,"准备重命名",-1))])])]),t("div",qt,[e[45]||(e[45]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",Wt,[l(a,null,{default:s(()=>[l(o(be))]),_:1})]),t("div",Gt,[t("div",Jt,g(rl.value),1),e[44]||(e[44]=t("div",{class:"stats-label"},"预览效果",-1)),t("div",Ht,[l(a,null,{default:s(()=>[l(o(M))]),_:1}),e[43]||(e[43]=t("span",null,"实时预览",-1))])]),u.value.length>100?(m(),f("div",Kt,[l(c,{title:`当前选择了 ${u.value.length} 个文件`,type:"info",description:`已启用性能优化模式。渲染时间: ${me.value.toFixed(1)}ms`,"show-icon":"",closable:!1},null,8,["title","description"])])):C("",!0)]),t("div",Qt,[e[48]||(e[48]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",Xt,[l(a,null,{default:s(()=>[l(o(ot))]),_:1})]),t("div",Yt,[t("div",es,g(al.value.length),1),e[47]||(e[47]=t("div",{class:"stats-label"},"保存模板",-1)),t("div",ls,[l(a,null,{default:s(()=>[l(o(it))]),_:1}),e[46]||(e[46]=t("span",null,"快速应用",-1))])])])])]),t("div",ts,[t("div",ss,[t("div",as,[t("div",ns,[t("h3",os,[l(a,null,{default:s(()=>[l(o(dt))]),_:1}),e[49]||(e[49]=v(" 文件选择 ",-1))]),t("div",is,[l(d,{size:"small",onClick:ml,disabled:x.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[l(o(we))]),_:1}),e[50]||(e[50]=v(" 全选 ",-1))]),_:1,__:[50]},8,["disabled"]),l(d,{size:"small",onClick:De,disabled:u.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[l(o(Xe))]),_:1}),e[51]||(e[51]=v(" 清空 ",-1))]),_:1,__:[51]},8,["disabled"]),l(d,{size:"small",onClick:Fl,disabled:x.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[W.value?(m(),se(o(ut),{key:1})):(m(),se(o(Ye),{key:0}))]),_:1}),v(" "+g(W.value?"收缩":"展开"),1)]),_:1},8,["disabled"]),l(T,{onCommand:Bl,disabled:x.value.length===0},{dropdown:s(()=>[l(w,null,{default:s(()=>[l(p,{command:"all-with-ext"},{default:s(()=>[l(a,null,{default:s(()=>[l(o($))]),_:1}),e[53]||(e[53]=v(" 复制所有文件名（含后缀） ",-1))]),_:1,__:[53]}),l(p,{command:"all-without-ext"},{default:s(()=>[l(a,null,{default:s(()=>[l(o(V))]),_:1}),e[54]||(e[54]=v(" 复制所有文件名（无后缀） ",-1))]),_:1,__:[54]}),l(p,{command:"selected-with-ext",disabled:u.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[l(o(we))]),_:1}),e[55]||(e[55]=v(" 复制选中文件名（含后缀） ",-1))]),_:1,__:[55]},8,["disabled"]),l(p,{command:"selected-without-ext",disabled:u.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[l(o(be))]),_:1}),e[56]||(e[56]=v(" 复制选中文件名（无后缀） ",-1))]),_:1,__:[56]},8,["disabled"])]),_:1})]),default:s(()=>[l(d,{size:"small",type:"primary"},{default:s(()=>[l(a,null,{default:s(()=>[l(o(el))]),_:1}),e[52]||(e[52]=v(" 批量复制 ",-1)),l(a,{class:"el-icon--right"},{default:s(()=>[l(o(Ye))]),_:1})]),_:1,__:[52]})]),_:1},8,["disabled"]),l(d,{size:"small",onClick:fe,loading:ie.value},{default:s(()=>[l(a,null,{default:s(()=>[l(o(rt))]),_:1}),e[57]||(e[57]=v(" 刷新 ",-1))]),_:1,__:[57]},8,["loading"])])]),t("div",ds,[x.value.length>0?(m(),f("div",us,[t("div",rs,[t("div",cs,[l(a,null,{default:s(()=>[l(o($))]),_:1}),t("span",null,"总计: "+g(x.value.length)+" 个文件",1)]),u.value.length>0?(m(),f("div",vs,[l(a,null,{default:s(()=>[l(o(we))]),_:1}),t("span",null,"已选: "+g(u.value.length)+" 个",1)])):C("",!0),t("div",ps,[l(a,null,{default:s(()=>[l(o(M))]),_:1}),t("span",null,"当前页: "+g(O.value.length)+" 个",1)])]),t("div",_s,[l(b,{modelValue:ce.value,"onUpdate:modelValue":e[1]||(e[1]=i=>ce.value=i),placeholder:"搜索文件名...",size:"small",clearable:"",onInput:Ll,style:{width:"200px"}},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(ne))]),_:1})]),_:1},8,["modelValue"])])])):C("",!0),Y.value.length===0&&!ie.value?(m(),f("div",ms,[t("div",fs,[t("div",gs,[l(a,null,{default:s(()=>[l(o(ct))]),_:1})]),t("div",ws,[e[59]||(e[59]=t("h4",null,"暂无文件",-1)),e[60]||(e[60]=t("p",null,"请先上传一些文件进行批量重命名",-1)),l(d,{type:"primary",size:"small",onClick:e[2]||(e[2]=i=>N.value=!0)},{default:s(()=>e[58]||(e[58]=[v(" 上传文件 ",-1)])),_:1,__:[58]})])])])):(m(),f("div",bs,[t("div",hs,[t("div",xs,[t("div",ys,[l(F,{"model-value":u.value.length===O.value.length&&O.value.length>0,indeterminate:u.value.length>0&&u.value.length<O.value.length,onChange:jl},null,8,["model-value","indeterminate"])]),e[61]||(e[61]=t("div",{class:"header-cell name-cell"},"文件名",-1)),e[62]||(e[62]=t("div",{class:"header-cell size-cell"},"大小",-1)),e[63]||(e[63]=t("div",{class:"header-cell index-cell"},"序号",-1)),e[64]||(e[64]=t("div",{class:"header-cell action-cell"},"操作",-1))]),t("div",ks,[(m(!0),f(Ee,null,Te(O.value,(i,le)=>(m(),f("div",{key:i.id,class:He(["table-row",{selected:u.value.includes(i)}]),onClick:Ue=>Oe(i)},[t("div",Vs,[l(F,{"model-value":u.value.includes(i),onChange:Ue=>Oe(i),onClick:e[3]||(e[3]=Ke(()=>{},["stop"]))},null,8,["model-value","onChange"])]),t("div",$s,[t("div",zs,[l(a,{class:"file-icon"},{default:s(()=>[(m(),se(Gl(_l(je(i.original_name)))))]),_:2},1024),t("span",Is,g(i.original_name),1)])]),t("div",Us,[t("span",Es,g(o(Zl)(i.size)),1)]),t("div",Ts,[t("span",Ss,"#"+g((B.value-1)*L.value+le+1),1)]),t("div",Rs,[l(T,{trigger:"click",onClick:e[4]||(e[4]=Ke(()=>{},["stop"]))},{dropdown:s(()=>[l(w,null,{default:s(()=>[l(p,{onClick:Ue=>El(i.original_name)},{default:s(()=>[l(a,null,{default:s(()=>[l(o(el))]),_:1}),e[65]||(e[65]=v(" 复制完整名称 ",-1))]),_:2,__:[65]},1032,["onClick"]),l(p,{onClick:Ue=>Tl(i.original_name)},{default:s(()=>[l(a,null,{default:s(()=>[l(o($))]),_:1}),e[66]||(e[66]=v(" 复制无后缀名称 ",-1))]),_:2,__:[66]},1032,["onClick"])]),_:2},1024)]),default:s(()=>[l(d,{text:"",size:"small",class:"action-btn"},{default:s(()=>[l(a,null,{default:s(()=>[l(o(vt))]),_:1})]),_:1})]),_:2},1024)])],10,Cs))),128))])]),Y.value.length>L.value?(m(),f("div",Ps,[l(ee,{"current-page":B.value,"onUpdate:currentPage":e[5]||(e[5]=i=>B.value=i),"page-size":L.value,"onUpdate:pageSize":e[6]||(e[6]=i=>L.value=i),"page-sizes":pl.value,total:Y.value.length,layout:"total, sizes, prev, pager, next, jumper",background:"",small:"",onSizeChange:Sl,onCurrentChange:Rl},null,8,["current-page","page-size","page-sizes","total"])])):C("",!0)]))])])]),t("div",Bs,[t("div",Ls,[t("div",Fs,[t("h3",js,[l(a,null,{default:s(()=>[l(o(pt))]),_:1}),e[67]||(e[67]=v(" 重命名规则 ",-1))]),t("div",Ds,[l(d,{size:"small",onClick:e[7]||(e[7]=i=>q.value=!0)},{default:s(()=>[l(a,null,{default:s(()=>[l(o(Se))]),_:1}),e[68]||(e[68]=v(" 批量导入 ",-1))]),_:1,__:[68]}),l(d,{size:"small",onClick:hl},{default:s(()=>[l(a,null,{default:s(()=>[l(o(be))]),_:1}),e[69]||(e[69]=v(" 智能模板 ",-1))]),_:1,__:[69]})])]),t("div",Os,[t("div",As,[t("div",Ms,[t("label",Zs,[l(a,null,{default:s(()=>[l(o(ll))]),_:1}),e[70]||(e[70]=v(" 重命名类型 ",-1))]),l(Dl,{modelValue:k.value,"onUpdate:modelValue":e[8]||(e[8]=i=>k.value=i),onChange:h,class:"modern-select",size:"large",placeholder:"选择重命名方式"},{default:s(()=>[l(j,{label:"🔤 正则表达式 - 使用正则匹配和替换",value:"regex"}),l(j,{label:"📝 前缀添加 - 在文件名前添加内容",value:"prefix"}),l(j,{label:"📄 后缀添加 - 在文件名后添加内容",value:"suffix"}),l(j,{label:"🔄 替换内容 - 查找并替换指定文本",value:"replace"}),l(j,{label:"🗂️ 映射替换 - 一对一数字映射替换",value:"mapping"}),l(j,{label:"🔢 序号重命名 - 按序号批量重命名",value:"sequence"}),l(j,{label:"🔧 扩展名替换 - 保留文件名但替换扩展名",value:"extension"})]),_:1},8,["modelValue"])]),k.value==="regex"?(m(),f("div",Ns,[t("div",qs,[t("label",Ws,[l(a,null,{default:s(()=>[l(o(ne))]),_:1}),e[71]||(e[71]=v(" 匹配模式 ",-1))]),l(b,{modelValue:G.value,"onUpdate:modelValue":e[9]||(e[9]=i=>G.value=i),placeholder:"例如: photo(\\d+)",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(ne))]),_:1})]),_:1},8,["modelValue"]),e[72]||(e[72]=t("div",{class:"input-hint"},"使用正则表达式匹配文件名模式",-1))]),t("div",Gs,[t("label",Js,[l(a,null,{default:s(()=>[l(o(V))]),_:1}),e[73]||(e[73]=v(" 替换内容 ",-1))]),l(b,{modelValue:J.value,"onUpdate:modelValue":e[10]||(e[10]=i=>J.value=i),placeholder:"例如: image_$1",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(V))]),_:1})]),_:1},8,["modelValue"]),e[74]||(e[74]=t("div",{class:"input-hint"},"使用 $1, $2 等引用捕获组",-1))])])):k.value==="prefix"?(m(),f("div",Hs,[t("div",Ks,[t("label",Qs,[l(a,null,{default:s(()=>[l(o(he))]),_:1}),e[75]||(e[75]=v(" 前缀内容 ",-1))]),l(b,{modelValue:D.value,"onUpdate:modelValue":e[11]||(e[11]=i=>D.value=i),placeholder:"例如: 新文件_",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(he))]),_:1})]),_:1},8,["modelValue"]),e[76]||(e[76]=t("div",{class:"input-hint"},"将在每个文件名前添加此内容",-1))])])):k.value==="suffix"?(m(),f("div",Xs,[t("div",Ys,[t("label",ea,[l(a,null,{default:s(()=>[l(o(he))]),_:1}),e[77]||(e[77]=v(" 后缀内容 ",-1))]),l(b,{modelValue:H.value,"onUpdate:modelValue":e[12]||(e[12]=i=>H.value=i),placeholder:"例如: _备份",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(he))]),_:1})]),_:1},8,["modelValue"]),e[78]||(e[78]=t("div",{class:"input-hint"},"将在每个文件名后添加此内容",-1))])])):k.value==="replace"?(m(),f("div",la,[t("div",ta,[t("label",sa,[l(a,null,{default:s(()=>[l(o(ne))]),_:1}),e[79]||(e[79]=v(" 查找内容 ",-1))]),l(b,{modelValue:K.value,"onUpdate:modelValue":e[13]||(e[13]=i=>K.value=i),placeholder:"要替换的文本",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(ne))]),_:1})]),_:1},8,["modelValue"]),e[80]||(e[80]=t("div",{class:"input-hint"},"输入要查找的文本内容",-1))]),t("div",aa,[t("label",na,[l(a,null,{default:s(()=>[l(o(V))]),_:1}),e[81]||(e[81]=v(" 替换为 ",-1))]),l(b,{modelValue:ve.value,"onUpdate:modelValue":e[14]||(e[14]=i=>ve.value=i),placeholder:"新的文本",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(V))]),_:1})]),_:1},8,["modelValue"]),e[82]||(e[82]=t("div",{class:"input-hint"},"输入替换后的文本内容",-1))])])):k.value==="mapping"?(m(),f("div",oa,[t("div",ia,[t("label",da,[l(a,null,{default:s(()=>[l(o($))]),_:1}),e[83]||(e[83]=v(" 原始数字列表 ",-1))]),l(b,{modelValue:z.value,"onUpdate:modelValue":e[15]||(e[15]=i=>z.value=i),type:"textarea",rows:6,placeholder:`请输入原始数字列表，每行一个或用空格分隔
例如：
9200190392641700162307
9200190392641700162291
9200190392641700162284`,onInput:Me,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o($))]),_:1})]),_:1},8,["modelValue"]),e[84]||(e[84]=t("div",{class:"input-hint"},"输入要被替换的原始数字，每行一个或用空格分隔",-1))]),t("div",ua,[t("label",ra,[l(a,null,{default:s(()=>[l(o(V))]),_:1}),e[85]||(e[85]=v(" 新数字列表 ",-1))]),l(b,{modelValue:I.value,"onUpdate:modelValue":e[16]||(e[16]=i=>I.value=i),type:"textarea",rows:6,placeholder:`请输入新数字列表，每行一个或用空格分隔
例如：
BA796022416314566
BA796452825314550
JG810462654321445`,onInput:Me,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(V))]),_:1})]),_:1},8,["modelValue"]),e[86]||(e[86]=t("div",{class:"input-hint"},"输入替换后的新数字，数量和顺序需与原始列表对应",-1))]),z.value||I.value?(m(),f("div",ca,[t("div",va,[l(a,null,{default:s(()=>[l(o(oe))]),_:1}),t("span",null,"原始数字: "+g(U(z.value).length)+" 个",1)]),t("div",pa,[l(a,null,{default:s(()=>[l(o(oe))]),_:1}),t("span",null,"新数字: "+g(U(I.value).length)+" 个",1)]),t("div",{class:He(["status-item",{"status-warning":U(z.value).length!==U(I.value).length}])},[l(a,null,{default:s(()=>[U(z.value).length!==U(I.value).length?(m(),se(o(Re),{key:0})):(m(),se(o(ae),{key:1}))]),_:1}),U(z.value).length===U(I.value).length?(m(),f("span",_a,"数量匹配")):(m(),f("span",ma,"数量不匹配，请检查"))],2)])):C("",!0)])):k.value==="sequence"?(m(),f("div",fa,[t("div",ga,[t("div",wa,[t("label",ba,[l(a,null,{default:s(()=>[l(o($))]),_:1}),e[87]||(e[87]=v(" 基础名称 ",-1))]),l(b,{modelValue:Q.value,"onUpdate:modelValue":e[17]||(e[17]=i=>Q.value=i),placeholder:"例如: 文件",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o($))]),_:1})]),_:1},8,["modelValue"])]),t("div",ha,[t("label",xa,[l(a,null,{default:s(()=>[l(o(be))]),_:1}),e[88]||(e[88]=v(" 起始数字 ",-1))]),l(qe,{modelValue:pe.value,"onUpdate:modelValue":e[18]||(e[18]=i=>pe.value=i),min:0,onChange:h,size:"large",class:"modern-number-input","controls-position":"right"},null,8,["modelValue"])])]),t("div",ya,[t("label",ka,[l(a,null,{default:s(()=>[l(o(ll))]),_:1}),e[89]||(e[89]=v(" 数字位数 ",-1))]),l(qe,{modelValue:_e.value,"onUpdate:modelValue":e[19]||(e[19]=i=>_e.value=i),min:1,max:10,onChange:h,size:"large",class:"modern-number-input","controls-position":"right"},null,8,["modelValue"]),e[90]||(e[90]=t("div",{class:"input-hint"},"设置序号的位数，如 001, 002...",-1))])])):k.value==="extension"?(m(),f("div",Ca,[t("div",Va,[t("label",$a,[l(a,null,{default:s(()=>[l(o(V))]),_:1}),e[91]||(e[91]=v(" 新扩展名 ",-1))]),l(b,{modelValue:X.value,"onUpdate:modelValue":e[20]||(e[20]=i=>X.value=i),placeholder:"例如: txt, pdf, jpg",onInput:h,size:"large",class:"modern-input"},{prefix:s(()=>[l(a,null,{default:s(()=>[l(o(V))]),_:1})]),_:1},8,["modelValue"]),e[92]||(e[92]=t("div",{class:"input-hint"},"输入新的文件扩展名（不需要包含点号）",-1))])])):C("",!0),t("div",za,[t("div",Ia,[l(d,{type:"primary",onClick:bl,disabled:u.value.length===0||ue.value,loading:Z.value,size:"large",class:"modern-action-btn primary-btn"},{default:s(()=>[l(a,null,{default:s(()=>[l(o(ge))]),_:1}),e[93]||(e[93]=t("span",null,"下载重命名文件",-1)),u.value.length>0?(m(),f("div",Ua,g(u.value.length),1)):C("",!0)]),_:1,__:[93]},8,["disabled","loading"]),l(d,{onClick:Ze,disabled:u.value.length===0||ue.value,size:"large",class:"modern-action-btn preview-btn"},{default:s(()=>[l(a,null,{default:s(()=>[l(o(M))]),_:1}),e[94]||(e[94]=t("span",null,"预览效果",-1))]),_:1,__:[94]},8,["disabled"])])]),ol.value?(m(),f("div",Ea,[t("div",Ta,[e[95]||(e[95]=t("h4",null,"批量重命名进度",-1)),t("span",Sa,g(ke.value)+" / "+g(Be.value),1)]),l(o(_t),{percentage:nl.value,status:re.value>0?"warning":"success","stroke-width":8,striped:"","striped-flow":""},null,8,["percentage","status"]),t("div",Ra,[t("span",Pa,"成功: "+g(ke.value-re.value),1),re.value>0?(m(),f("span",Ba,"失败: "+g(re.value),1)):C("",!0),t("span",La,"剩余: "+g(Be.value-ke.value),1)])])):C("",!0)])])])])]),E.value.length>0?(m(),f("div",Fa,[t("div",ja,[t("h2",Da,[l(a,null,{default:s(()=>[l(o(M))]),_:1}),e[96]||(e[96]=v(" 预览效果 ",-1))]),l(d,{onClick:e[21]||(e[21]=i=>E.value=[]),text:""},{default:s(()=>[l(a,null,{default:s(()=>[l(o(Xe))]),_:1}),e[97]||(e[97]=v(" 关闭预览 ",-1))]),_:1,__:[97]})]),t("div",Oa,[t("div",Aa,[(m(!0),f(Ee,null,Te(E.value,(i,le)=>(m(),f("div",{key:le,class:"preview-item"},[t("div",Ma,[l(a,null,{default:s(()=>[l(o($))]),_:1}),t("span",null,g(i.original),1)]),t("div",Za,[l(a,null,{default:s(()=>[l(o(tl))]),_:1})]),t("div",Na,[l(a,null,{default:s(()=>[l(o(V))]),_:1}),t("span",null,g(i.new),1)])]))),128))])])])):C("",!0),l(Ie,{modelValue:N.value,"onUpdate:modelValue":e[22]||(e[22]=i=>N.value=i),title:"上传文件进行批量重命名",width:"600px","close-on-click-modal":!1},{default:s(()=>[t("div",qa,[t("div",Wa,[l(We,{ref:"uploadRef",action:cl.value,headers:vl.value,"on-success":yl,"on-error":kl,"before-upload":xl,"on-progress":Cl,name:"files",multiple:"",drag:"",class:"modern-upload"},{tip:s(()=>[t("div",Ja,[t("div",Ha,[l(a,null,{default:s(()=>[l(o(oe))]),_:1}),e[99]||(e[99]=t("span",null,"支持多文件同时上传",-1))]),t("div",Ka,[l(a,null,{default:s(()=>[l(o(Re))]),_:1}),e[100]||(e[100]=t("span",null,"单个文件不超过100MB",-1))]),t("div",Qa,[l(a,null,{default:s(()=>[l(o(ae))]),_:1}),e[101]||(e[101]=t("span",null,"上传后可立即进行批量重命名",-1))])])]),default:s(()=>[t("div",Ga,[l(a,{class:"upload-icon"},{default:s(()=>[l(o(sl))]),_:1}),e[98]||(e[98]=t("div",{class:"upload-text"},[t("div",{class:"upload-title"},"拖拽文件到此处上传"),t("div",{class:"upload-subtitle"},[v("或 "),t("em",null,"点击选择文件")])],-1))])]),_:1},8,["action","headers"])])])]),_:1},8,["modelValue"]),l(Ie,{modelValue:de.value,"onUpdate:modelValue":e[25]||(e[25]=i=>de.value=i),title:"导出重命名后的文件",width:"500px","close-on-click-modal":!1},{footer:s(()=>[t("div",tn,[l(d,{onClick:e[24]||(e[24]=i=>de.value=!1)},{default:s(()=>e[105]||(e[105]=[v("取消",-1)])),_:1,__:[105]}),l(d,{type:"primary",onClick:Vl,loading:R.value},{default:s(()=>[l(a,null,{default:s(()=>[l(o(ge))]),_:1}),e[106]||(e[106]=v(" 开始导出 ",-1))]),_:1,__:[106]},8,["loading"])])]),default:s(()=>[t("div",Xa,[t("div",Ya,[l(a,{class:"export-icon"},{default:s(()=>[l(o(ge))]),_:1}),t("div",en,[t("h3",null,"准备导出 "+g(u.value.length)+" 个文件",1),e[102]||(e[102]=t("p",null,"重命名完成后，您可以选择导出方式：",-1))])]),t("div",ln,[l(Ol,{modelValue:ye.value,"onUpdate:modelValue":e[23]||(e[23]=i=>ye.value=i)},{default:s(()=>[l(Ge,{label:"zip"},{default:s(()=>e[103]||(e[103]=[v("打包为ZIP文件下载",-1)])),_:1,__:[103]}),l(Ge,{label:"individual"},{default:s(()=>e[104]||(e[104]=[v("逐个下载文件",-1)])),_:1,__:[104]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"]),l(Ie,{modelValue:q.value,"onUpdate:modelValue":e[29]||(e[29]=i=>q.value=i),title:"批量导入重命名规则",width:"700px","close-on-click-modal":!1},{footer:s(()=>[t("div",xn,[l(d,{onClick:e[28]||(e[28]=i=>q.value=!1)},{default:s(()=>e[113]||(e[113]=[v("取消",-1)])),_:1,__:[113]}),l(d,{onClick:Ne,disabled:!P.value.trim()},{default:s(()=>[l(a,null,{default:s(()=>[l(o(M))]),_:1}),e[114]||(e[114]=v(" 预览规则 ",-1))]),_:1,__:[114]},8,["disabled"]),l(d,{type:"primary",onClick:Ul,disabled:y.value.length===0},{default:s(()=>[l(a,null,{default:s(()=>[l(o(ae))]),_:1}),v(" 应用规则 ("+g(y.value.length)+") ",1)]),_:1},8,["disabled"])])]),default:s(()=>[t("div",sn,[t("div",an,[l(a,{class:"import-icon"},{default:s(()=>[l(o(Se))]),_:1}),e[107]||(e[107]=t("div",{class:"import-text"},[t("h3",null,"批量导入重命名规则"),t("p",null,"支持导入CSV格式的重命名规则，每行格式：原文件名,新文件名")],-1))]),t("div",nn,[l(Al,{modelValue:Pe.value,"onUpdate:modelValue":e[27]||(e[27]=i=>Pe.value=i),type:"border-card"},{default:s(()=>[l(Je,{label:"文本输入",name:"text"},{default:s(()=>[t("div",on,[l(b,{modelValue:P.value,"onUpdate:modelValue":e[26]||(e[26]=i=>P.value=i),type:"textarea",rows:10,placeholder:`请输入重命名规则，每行格式：原文件名,新文件名
例如：
photo1.jpg,vacation_001.jpg
photo2.jpg,vacation_002.jpg
document.pdf,report_2025.pdf`},null,8,["modelValue"]),t("div",dn,[t("div",un,[l(a,null,{default:s(()=>[l(o(oe))]),_:1}),e[108]||(e[108]=t("span",null,"每行一个重命名规则，用逗号分隔原文件名和新文件名",-1))]),t("div",rn,[l(a,null,{default:s(()=>[l(o(Re))]),_:1}),e[109]||(e[109]=t("span",null,"确保原文件名与系统中的文件完全匹配",-1))])])])]),_:1}),l(Je,{label:"文件上传",name:"file"},{default:s(()=>[t("div",cn,[l(We,{ref:"importUploadRef","auto-upload":!1,"show-file-list":!0,accept:".csv,.txt","on-change":Il,drag:""},{tip:s(()=>[t("div",vn,[t("div",pn,[l(a,null,{default:s(()=>[l(o(oe))]),_:1}),e[110]||(e[110]=t("span",null,"支持CSV和TXT格式文件",-1))]),t("div",_n,[l(a,null,{default:s(()=>[l(o(ae))]),_:1}),e[111]||(e[111]=t("span",null,"文件格式：原文件名,新文件名",-1))])])]),default:s(()=>[l(a,{class:"upload-icon"},{default:s(()=>[l(o(sl))]),_:1}),e[112]||(e[112]=t("div",{class:"upload-text"},[t("div",{class:"upload-title"},"拖拽CSV文件到此处"),t("div",{class:"upload-subtitle"},[v("或 "),t("em",null,"点击选择文件")])],-1))]),_:1,__:[112]},512)])]),_:1})]),_:1},8,["modelValue"])]),y.value.length>0?(m(),f("div",mn,[t("h4",null,"导入预览 ("+g(y.value.length)+" 条规则)",1),t("div",fn,[(m(!0),f(Ee,null,Te(y.value.slice(0,5),(i,le)=>(m(),f("div",{key:le,class:"preview-item"},[t("div",gn,[l(a,null,{default:s(()=>[l(o($))]),_:1}),t("span",null,g(i.original),1)]),t("div",wn,[l(a,null,{default:s(()=>[l(o(tl))]),_:1})]),t("div",bn,[l(a,null,{default:s(()=>[l(o(V))]),_:1}),t("span",null,g(i.new),1)])]))),128)),y.value.length>5?(m(),f("div",hn," 还有 "+g(y.value.length-5)+" 条规则... ",1)):C("",!0)])])):C("",!0)])]),_:1},8,["modelValue"])])}}});const Tn=Nl(yn,[["__scopeId","data-v-9578d984"]]);export{Tn as default};
