import{a as U,_ as ut}from"./index-4cbb9fd4.js";/* empty css                        *//* empty css                    *//* empty css               *//* empty css                 */import{E as w,a as _t,q as pt,x as vt,aq as mt,z as ft,A as gt,C as ht,w as yt,a5 as bt,a6 as Ct,v as kt}from"./elementPlus-b5b9032b.js";import{x as wt,r as v,c as E,k as Dt,z as C,A as e,Q as s,I as a,M as _,O as u,J as xt,al as d,y as r,C as zt,H as g}from"./vendor-a56c79b3.js";const St={class:"operation-logs-page"},Et={class:"page-header"},Mt={class:"header-content"},Lt={class:"header-text"},Tt={class:"page-title"},Ot={class:"header-actions"},Rt={class:"stats-overview"},Vt={class:"stat-card primary"},Ut={class:"stat-icon"},Bt={class:"stat-content"},It={class:"stat-value"},At={class:"stat-card success"},Pt={class:"stat-icon"},$t={class:"stat-content"},Nt={class:"stat-value"},jt={class:"stat-card danger"},qt={class:"stat-icon"},Ft={class:"stat-content"},Ht={class:"stat-value"},Jt={class:"stat-card info"},Qt={class:"stat-icon"},Gt={class:"stat-content"},Kt={class:"stat-value"},Wt={class:"filter-card"},Xt={class:"filter-header"},Yt={class:"filter-title"},Zt={class:"filter-content"},te={class:"filter-row"},ee={class:"filter-item"},se={class:"option-content"},ae={class:"option-content"},oe={class:"option-content"},le={class:"option-content"},ne={class:"filter-item"},ie={class:"option-content"},de={class:"option-content"},ce={class:"filter-item"},re={class:"filter-actions"},ue={class:"logs-card"},_e={class:"card-header"},pe={class:"card-title"},ve={class:"card-actions"},me={class:"card-content"},fe={key:0,class:"empty-state"},ge={class:"empty-illustration"},he={class:"empty-icon"},ye={class:"empty-content"},be={key:1,class:"logs-list"},Ce={class:"table-container"},ke={class:"action-cell"},we={class:"action-text"},De={class:"resource-cell"},xe={class:"resource-name"},ze={key:0,class:"change-detail"},Se={class:"change-item"},Ee={class:"old-value"},Me={class:"change-arrow"},Le={class:"change-item"},Te={class:"new-value"},Oe={key:1,class:"no-change"},Re={class:"time-cell"},Ve={class:"time-date"},Ue={class:"time-relative"},Be={class:"action-buttons"},Ie={class:"pagination-container"},Ae=wt({__name:"OperationLogs",setup(Pe){const h=v([]),k=v(!1),M=v(!1),D=v(1),L=v(20),x=v(0),T=v(),z=v([]),i=v({action:"",status:"",start_date:"",end_date:""}),N=E(()=>h.value.filter(o=>o.success===!0).length),j=E(()=>h.value.filter(o=>o.success===!1).length),q=E(()=>{const o=new Date().toDateString();return h.value.filter(t=>new Date(t.created_at).toDateString()===o).length}),F=E(()=>{const o=x.value,t=[10,20,50];if(o<=50)return t;if(o<=200)return[...t,100];if(o<=500)return[...t,100,200,500];if(o<=1e3)return[...t,100,200,500,1e3,o];if(o<=5e3)return[...t,100,200,500,1e3,2e3,Math.ceil(o/2),o];{const f=Math.ceil(o/4),n=Math.ceil(o/2);return[...t,100,200,500,1e3,2e3,f,n,o]}}),m=async()=>{try{k.value=!0;const o={page:D.value,limit:L.value,action:i.value.action,status:i.value.status,start_date:i.value.start_date,end_date:i.value.end_date},t=await U.logs.list(o);h.value=t.data.data.logs,x.value=t.data.data.pagination.total}catch(o){console.error("加载日志失败:",o),w.error("加载日志失败")}finally{k.value=!1}},H=o=>{o?(i.value.start_date=o[0].toISOString().split("T")[0],i.value.end_date=o[1].toISOString().split("T")[0]):(i.value.start_date="",i.value.end_date="")},B=()=>{i.value={action:"",status:"",start_date:"",end_date:""},T.value=void 0,D.value=1,m()},J=o=>({upload:"action-upload",download:"action-download",delete:"action-delete",rename:"action-rename"})[o]||"action-default",Q=o=>({upload:"上传",download:"下载",delete:"删除",rename:"重命名"})[o]||o,G=async o=>{try{await yt.confirm("确定要删除这条日志记录吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),z.value.push(o.id),await U.logs.delete(o.id),w.success("日志删除成功"),m()}catch(t){t!=="cancel"&&(console.error("删除日志失败:",t),w.error("删除日志失败"))}finally{z.value=z.value.filter(t=>t!==o.id)}},K=async()=>{try{M.value=!0;const o={action:i.value.action,status:i.value.status,start_date:i.value.start_date,end_date:i.value.end_date,format:"csv"},t=await U.logs.export(o),f=new Blob([t.data],{type:"text/csv"}),n=window.URL.createObjectURL(f),c=document.createElement("a");c.href=n,c.download=`operation_logs_${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(n),w.success("日志导出成功")}catch(o){console.error("导出日志失败:",o),w.error("导出日志失败")}finally{M.value=!1}},W=o=>new Date(o).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),X=o=>{const t=new Date,f=new Date(o),n=t.getTime()-f.getTime(),c=Math.floor(n/(1e3*60)),p=Math.floor(n/(1e3*60*60)),S=Math.floor(n/(1e3*60*60*24));return c<1?"刚刚":c<60?`${c}分钟前`:p<24?`${p}小时前`:S<7?`${S}天前`:"一周前"};return Dt(()=>{m()}),(o,t)=>{const f=d("DocumentCopy"),n=_t,c=d("Download"),p=pt,S=d("DataAnalysis"),O=d("CircleCheck"),R=d("CircleClose"),Y=d("Clock"),Z=d("Filter"),I=d("Refresh"),A=d("Upload"),y=bt,V=d("Delete"),P=d("Edit"),$=vt,tt=mt,et=d("Search"),st=d("List"),at=d("DocumentRemove"),ot=d("Operation"),b=Ct,lt=d("Document"),nt=d("Right"),it=kt,dt=ft,ct=gt,rt=ht;return r(),C("div",St,[e("div",Et,[e("div",Mt,[e("div",Lt,[e("h1",Tt,[s(n,null,{default:a(()=>[s(f)]),_:1}),t[5]||(t[5]=_(" 操作日志 ",-1))]),t[6]||(t[6]=e("p",{class:"page-subtitle"},"查看和管理系统操作记录，追踪文件变更历史",-1))]),e("div",Ot,[s(p,{type:"primary",size:"large",onClick:K,loading:M.value},{default:a(()=>[s(n,null,{default:a(()=>[s(c)]),_:1}),t[7]||(t[7]=_(" 导出日志 ",-1))]),_:1,__:[7]},8,["loading"])])])]),e("div",Rt,[e("div",Vt,[e("div",Ut,[s(n,null,{default:a(()=>[s(S)]),_:1})]),e("div",Bt,[e("div",It,u(x.value),1),t[8]||(t[8]=e("div",{class:"stat-label"},"总操作数",-1))])]),e("div",At,[e("div",Pt,[s(n,null,{default:a(()=>[s(O)]),_:1})]),e("div",$t,[e("div",Nt,u(N.value),1),t[9]||(t[9]=e("div",{class:"stat-label"},"成功操作",-1))])]),e("div",jt,[e("div",qt,[s(n,null,{default:a(()=>[s(R)]),_:1})]),e("div",Ft,[e("div",Ht,u(j.value),1),t[10]||(t[10]=e("div",{class:"stat-label"},"失败操作",-1))])]),e("div",Jt,[e("div",Qt,[s(n,null,{default:a(()=>[s(Y)]),_:1})]),e("div",Gt,[e("div",Kt,u(q.value),1),t[11]||(t[11]=e("div",{class:"stat-label"},"今日操作",-1))])])]),e("div",Wt,[e("div",Xt,[e("div",Yt,[s(n,null,{default:a(()=>[s(Z)]),_:1}),t[12]||(t[12]=_(" 筛选条件 ",-1))]),s(p,{text:"",onClick:B},{default:a(()=>[s(n,null,{default:a(()=>[s(I)]),_:1}),t[13]||(t[13]=_(" 重置 ",-1))]),_:1,__:[13]})]),e("div",Zt,[e("div",te,[e("div",ee,[t[18]||(t[18]=e("label",null,"操作类型",-1)),s($,{modelValue:i.value.action,"onUpdate:modelValue":t[0]||(t[0]=l=>i.value.action=l),placeholder:"选择操作类型",clearable:"",size:"large"},{default:a(()=>[s(y,{label:"上传文件",value:"upload"},{default:a(()=>[e("div",se,[s(n,null,{default:a(()=>[s(A)]),_:1}),t[14]||(t[14]=e("span",null,"上传文件",-1))])]),_:1}),s(y,{label:"下载文件",value:"download"},{default:a(()=>[e("div",ae,[s(n,null,{default:a(()=>[s(c)]),_:1}),t[15]||(t[15]=e("span",null,"下载文件",-1))])]),_:1}),s(y,{label:"删除文件",value:"delete"},{default:a(()=>[e("div",oe,[s(n,null,{default:a(()=>[s(V)]),_:1}),t[16]||(t[16]=e("span",null,"删除文件",-1))])]),_:1}),s(y,{label:"重命名文件",value:"rename"},{default:a(()=>[e("div",le,[s(n,null,{default:a(()=>[s(P)]),_:1}),t[17]||(t[17]=e("span",null,"重命名文件",-1))])]),_:1})]),_:1},8,["modelValue"])]),e("div",ne,[t[21]||(t[21]=e("label",null,"操作状态",-1)),s($,{modelValue:i.value.status,"onUpdate:modelValue":t[1]||(t[1]=l=>i.value.status=l),placeholder:"选择状态",clearable:"",size:"large"},{default:a(()=>[s(y,{label:"成功",value:"success"},{default:a(()=>[e("div",ie,[s(n,null,{default:a(()=>[s(O)]),_:1}),t[19]||(t[19]=e("span",null,"成功",-1))])]),_:1}),s(y,{label:"失败",value:"failed"},{default:a(()=>[e("div",de,[s(n,null,{default:a(()=>[s(R)]),_:1}),t[20]||(t[20]=e("span",null,"失败",-1))])]),_:1})]),_:1},8,["modelValue"])]),e("div",ce,[t[22]||(t[22]=e("label",null,"时间范围",-1)),s(tt,{modelValue:T.value,"onUpdate:modelValue":t[2]||(t[2]=l=>T.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"large",onChange:H},null,8,["modelValue"])]),e("div",re,[s(p,{type:"primary",size:"large",onClick:m,loading:k.value},{default:a(()=>[s(n,null,{default:a(()=>[s(et)]),_:1}),t[23]||(t[23]=_(" 搜索 ",-1))]),_:1,__:[23]},8,["loading"])])])])]),e("div",ue,[e("div",_e,[e("div",pe,[s(n,null,{default:a(()=>[s(st)]),_:1}),t[24]||(t[24]=_(" 操作记录 ",-1))]),e("div",ve,[s(p,{text:"",onClick:m},{default:a(()=>[s(n,null,{default:a(()=>[s(I)]),_:1}),t[25]||(t[25]=_(" 刷新 ",-1))]),_:1,__:[25]})])]),e("div",me,[!k.value&&h.value.length===0?(r(),C("div",fe,[e("div",ge,[e("div",he,[s(n,null,{default:a(()=>[s(at)]),_:1})]),e("div",ye,[t[27]||(t[27]=e("h3",null,"暂无操作记录",-1)),t[28]||(t[28]=e("p",null,"当前筛选条件下没有找到操作记录",-1)),s(p,{type:"primary",onClick:B},{default:a(()=>t[26]||(t[26]=[_(" 重置筛选条件 ",-1)])),_:1,__:[26]})])])])):(r(),C("div",be,[xt((r(),C("div",Ce,[s(dt,{data:h.value,class:"logs-table"},{default:a(()=>[s(b,{label:"操作",width:"120"},{default:a(({row:l})=>[e("div",ke,[e("div",{class:zt(["action-icon",J(l.operation_type)])},[s(n,null,{default:a(()=>[l.operation_type==="upload"?(r(),g(A,{key:0})):l.operation_type==="download"?(r(),g(c,{key:1})):l.operation_type==="delete"?(r(),g(V,{key:2})):l.operation_type==="rename"?(r(),g(P,{key:3})):(r(),g(ot,{key:4}))]),_:2},1024)],2),e("span",we,u(Q(l.operation_type)),1)])]),_:1}),s(b,{prop:"file_path",label:"资源","min-width":"200"},{default:a(({row:l})=>[e("div",De,[s(n,{class:"file-icon"},{default:a(()=>[s(lt)]),_:1}),e("span",xe,u(l.file_path||"未知资源"),1)])]),_:1}),s(b,{label:"变更详情","min-width":"250"},{default:a(({row:l})=>[l.old_name&&l.new_name?(r(),C("div",ze,[e("div",Se,[t[29]||(t[29]=e("span",{class:"change-label"},"原名称:",-1)),e("span",Ee,u(l.old_name),1)]),e("div",Me,[s(n,null,{default:a(()=>[s(nt)]),_:1})]),e("div",Le,[t[30]||(t[30]=e("span",{class:"change-label"},"新名称:",-1)),e("span",Te,u(l.new_name),1)])])):(r(),C("div",Oe,t[31]||(t[31]=[e("span",null,"无变更信息",-1)])))]),_:1}),s(b,{label:"状态",width:"100"},{default:a(({row:l})=>[s(it,{type:l.success?"success":"danger",size:"large",class:"status-tag"},{default:a(()=>[s(n,null,{default:a(()=>[l.success?(r(),g(O,{key:0})):(r(),g(R,{key:1}))]),_:2},1024),_(" "+u(l.success?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1}),s(b,{prop:"created_at",label:"操作时间",width:"180"},{default:a(({row:l})=>[e("div",Re,[e("div",Ve,u(W(l.created_at)),1),e("div",Ue,u(X(l.created_at)),1)])]),_:1}),s(b,{label:"操作",width:"120",fixed:"right"},{default:a(({row:l})=>[e("div",Be,[s(p,{size:"small",type:"danger",text:"",onClick:$e=>G(l),loading:z.value.includes(l.id)},{default:a(()=>[s(n,null,{default:a(()=>[s(V)]),_:1}),t[32]||(t[32]=_(" 删除 ",-1))]),_:2,__:[32]},1032,["onClick","loading"])])]),_:1})]),_:1},8,["data"])])),[[rt,k.value]]),e("div",Ie,[s(ct,{"current-page":D.value,"onUpdate:currentPage":t[3]||(t[3]=l=>D.value=l),"page-size":L.value,"onUpdate:pageSize":t[4]||(t[4]=l=>L.value=l),total:x.value,"page-sizes":F.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m,onCurrentChange:m,background:""},null,8,["current-page","page-size","total","page-sizes"])])]))])])])}}});const Ge=ut(Ae,[["__scopeId","data-v-67bbcf3a"]]);export{Ge as default};
