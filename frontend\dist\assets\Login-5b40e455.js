import{u as w,_ as k}from"./index-4cbb9fd4.js";/* empty css                     *//* empty css                 */import{x,r as _,X as V,k as E,n as F,z as c,A as n,aD as z,Q as s,I as r,V as C,az as I,al as L,y as p,a5 as M,M as N}from"./vendor-a56c79b3.js";import{n as R,E as g,o as S,p as B}from"./elementPlus-b5b9032b.js";const q={class:"auth-container"},A={class:"auth-card"},K=["disabled"],T={key:0,class:"loading"},U={key:1},D={class:"auth-footer"},Q=x({__name:"Login",setup(X){const f=I(),v=w(),o=_(),l=_(!1),a=V({username:"",password:""}),h={username:[{required:!0,message:"请输入用户名",trigger:["blur","change"]},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:["blur","change"]}],password:[{required:!0,message:"请输入密码",trigger:["blur","change"]},{min:6,message:"密码长度不能少于 6 个字符",trigger:["blur","change"]}]},i=async()=>{if(o.value)try{await o.value.validate(),l.value=!0;const t=await v.login(a);t.success?(g.success("登录成功"),f.push("/dashboard")):g.error(t.message||"登录失败")}catch(t){console.error("登录失败:",t)}finally{l.value=!1}};return E(()=>{F(()=>{o.value&&o.value.clearValidate()})}),(t,e)=>{const m=S,d=B,b=R,y=L("router-link");return p(),c("div",q,[n("div",A,[e[4]||(e[4]=z('<div class="auth-header" data-v-35e0d856><div class="auth-logo" data-v-35e0d856><i class="el-icon-folder-opened" data-v-35e0d856></i></div><h1 class="auth-title" data-v-35e0d856>文件管理工具集</h1><p class="auth-subtitle" data-v-35e0d856>请登录您的账户</p></div>',1)),s(b,{ref_key:"loginFormRef",ref:o,model:a,rules:h,class:"auth-form",onSubmit:C(i,["prevent"])},{default:r(()=>[s(d,{prop:"username"},{default:r(()=>[s(m,{modelValue:a.username,"onUpdate:modelValue":e[0]||(e[0]=u=>a.username=u),placeholder:"用户名",size:"large","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),s(d,{prop:"password"},{default:r(()=>[s(m,{modelValue:a.password,"onUpdate:modelValue":e[1]||(e[1]=u=>a.password=u),type:"password",placeholder:"密码",size:"large","prefix-icon":"el-icon-lock","show-password":"",onKeyup:M(i,["enter"])},null,8,["modelValue"])]),_:1}),s(d,null,{default:r(()=>[n("button",{type:"button",disabled:l.value,class:"btn btn-primary btn-xl",onClick:i},[l.value?(p(),c("span",T)):(p(),c("span",U,"登录"))],8,K)]),_:1})]),_:1},8,["model"]),n("div",D,[e[3]||(e[3]=n("span",null,"还没有账户？",-1)),s(y,{to:"/register",class:"auth-link"},{default:r(()=>e[2]||(e[2]=[N("立即注册",-1)])),_:1,__:[2]})])])])}}});const P=k(Q,[["__scopeId","data-v-35e0d856"]]);export{P as default};
