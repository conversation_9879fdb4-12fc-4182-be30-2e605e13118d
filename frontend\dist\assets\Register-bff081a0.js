import{u as y,_ as b}from"./index-4cbb9fd4.js";/* empty css                     *//* empty css                 */import{x,r as g,X as k,k as E,n as R,z as p,A as u,aD as P,Q as a,I as t,V as z,az as C,al as F,y as c,a5 as q,M as I}from"./vendor-a56c79b3.js";import{n as M,E as f,o as N,p as S}from"./elementPlus-b5b9032b.js";const U={class:"auth-container"},B={class:"auth-card"},A=["disabled"],D={key:0,class:"loading"},K={key:1},T={class:"auth-footer"},Q=x({__name:"Register",setup(X){const _=C(),v=y(),l=g(),d=g(!1),s=k({username:"",email:"",password:"",confirmPassword:""}),w={username:[{required:!0,message:"请输入用户名",trigger:["blur","change"]},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:["blur","change"]}],email:[{required:!0,message:"请输入邮箱地址",trigger:["blur","change"]},{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],password:[{required:!0,message:"请输入密码",trigger:["blur","change"]},{min:6,message:"密码长度不能少于 6 个字符",trigger:["blur","change"]}],confirmPassword:[{required:!0,validator:(n,e,r)=>{e===""?r(new Error("请再次输入密码")):e!==s.password?r(new Error("两次输入密码不一致")):r()},trigger:["blur","change"]}]},m=async()=>{if(l.value)try{await l.value.validate(),d.value=!0;const n={username:s.username,email:s.email,password:s.password},e=await v.register(n);e.success?(f.success("注册成功"),_.push("/dashboard")):f.error(e.message||"注册失败")}catch(n){console.error("注册失败:",n)}finally{d.value=!1}};return E(()=>{R(()=>{l.value&&l.value.clearValidate()})}),(n,e)=>{const r=N,i=S,h=M,V=F("router-link");return c(),p("div",U,[u("div",B,[e[6]||(e[6]=P('<div class="auth-header" data-v-194d8235><div class="auth-logo" data-v-194d8235><i class="el-icon-user-solid" data-v-194d8235></i></div><h1 class="auth-title" data-v-194d8235>创建新账户</h1><p class="auth-subtitle" data-v-194d8235>加入文件管理工具集</p></div>',1)),a(h,{ref_key:"registerFormRef",ref:l,model:s,rules:w,class:"auth-form",onSubmit:z(m,["prevent"])},{default:t(()=>[a(i,{prop:"username"},{default:t(()=>[a(r,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=o=>s.username=o),placeholder:"用户名",size:"large","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),a(i,{prop:"email"},{default:t(()=>[a(r,{modelValue:s.email,"onUpdate:modelValue":e[1]||(e[1]=o=>s.email=o),placeholder:"邮箱地址",size:"large","prefix-icon":"el-icon-message"},null,8,["modelValue"])]),_:1}),a(i,{prop:"password"},{default:t(()=>[a(r,{modelValue:s.password,"onUpdate:modelValue":e[2]||(e[2]=o=>s.password=o),type:"password",placeholder:"密码",size:"large","prefix-icon":"el-icon-lock","show-password":""},null,8,["modelValue"])]),_:1}),a(i,{prop:"confirmPassword"},{default:t(()=>[a(r,{modelValue:s.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=o=>s.confirmPassword=o),type:"password",placeholder:"确认密码",size:"large","prefix-icon":"el-icon-lock","show-password":"",onKeyup:q(m,["enter"])},null,8,["modelValue"])]),_:1}),a(i,null,{default:t(()=>[u("button",{type:"button",disabled:d.value,class:"btn btn-primary btn-xl",onClick:m},[d.value?(c(),p("span",D)):(c(),p("span",K,"注册"))],8,A)]),_:1})]),_:1},8,["model"]),u("div",T,[e[5]||(e[5]=u("span",null,"已有账户？",-1)),a(V,{to:"/login",class:"auth-link"},{default:t(()=>e[4]||(e[4]=[I("立即登录",-1)])),_:1,__:[4]})])])])}}});const W=b(Q,[["__scopeId","data-v-194d8235"]]);export{W as default};
