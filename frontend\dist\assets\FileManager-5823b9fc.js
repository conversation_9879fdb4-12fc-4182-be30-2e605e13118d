import{u as ct,a as L,f as J,d as vt,_ as _t}from"./index-4cbb9fd4.js";/* empty css                        *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                 */import{x as pt,r as _,c as S,k as ft,z as p,A as t,Q as s,I as a,M as c,O as d,L as f,u as i,J as he,H as y,P as W,a4 as Z,n as ye,y as u,K as ke,C as we,V as be}from"./vendor-a56c79b3.js";import{E as m,w as I,a as gt,q as mt,o as ht,x as yt,y as kt,z as wt,A as bt,B as Ct,C as zt,D as Ce,F as ze,G as xe,H as D,I as Me,J as $e,K as xt,L as Mt,M as Se,N as ee,O as $t,P as St,Q as Ft,R as Bt,S as Tt,T as Q,U as Fe,V as Be,W as Dt,X as Et,Y as Vt,Z as te,_ as Te,$ as De,a0 as Ut,h as Pt,a1 as Lt,a2 as qt,a3 as jt,a4 as Ee,a5 as At,a6 as Ot,a7 as Ht,a8 as Nt,v as Rt}from"./elementPlus-b5b9032b.js";const Kt={class:"file-manager-page"},Yt={class:"file-manager-banner"},Jt={class:"banner-content"},Qt={class:"banner-text"},Gt={class:"title-section"},Xt={class:"main-icon"},Wt={class:"banner-actions"},Zt={class:"banner-visual"},It={class:"file-stack"},es={class:"file-layer layer-1"},ts={class:"file-layer layer-2"},ss={class:"file-layer layer-3"},ls={class:"stats-section"},as={class:"section-title"},os={class:"stats-grid"},ns={class:"stats-card primary"},is={class:"stats-icon"},us={class:"stats-content"},rs={class:"stats-value"},ds={class:"stats-trend"},cs={key:0,class:"stats-card success"},vs={class:"stats-icon"},_s={class:"stats-content"},ps={class:"stats-value"},fs={class:"stats-trend"},gs={class:"stats-card warning"},ms={class:"stats-icon"},hs={class:"stats-content"},ys={class:"stats-value"},ks={class:"stats-trend"},ws={class:"stats-card info"},bs={class:"stats-icon"},Cs={class:"stats-content"},zs={class:"stats-value"},xs={class:"stats-trend"},Ms={class:"quick-actions-section"},$s={class:"section-title"},Ss={class:"quick-actions-grid"},Fs={class:"action-icon"},Bs={class:"action-arrow"},Ts={class:"action-icon"},Ds={class:"action-arrow"},Es={class:"action-icon"},Vs={class:"action-arrow"},Us={class:"action-icon"},Ps={class:"action-content"},Ls={class:"action-arrow"},qs={class:"file-browser-section"},js={class:"section-header"},As={class:"section-title"},Os={class:"browser-controls"},Hs={class:"search-container"},Ns={class:"browser-content"},Rs={key:0,class:"batch-actions-bar"},Ks={class:"selected-info"},Ys={class:"batch-actions"},Js={key:1,class:"table-container"},Qs={class:"file-info"},Gs={key:2,class:"file-grid-container"},Xs={key:0,class:"empty-state"},Ws={class:"empty-illustration"},Zs={class:"empty-icon"},Is={class:"empty-content"},el={key:1,class:"file-grid"},tl=["onClick"],sl={class:"file-item-icon"},ll={class:"file-item-name"},al={class:"file-item-info"},ol={class:"file-size"},nl={class:"file-item-actions"},il={key:3,class:"pagination-container"},ul={key:0,class:"load-more-container"},rl={class:"load-more-info"},dl={class:"upload-dialog-content"},cl={key:0,class:"upload-area"},vl={class:"upload-content"},_l={class:"upload-tips"},pl={class:"tip-item"},fl={class:"tip-item"},gl={class:"tip-item"},ml={key:1,class:"upload-progress-section"},hl={class:"progress-header"},yl={class:"overall-progress"},kl={class:"progress-info"},wl={class:"file-progress-list"},bl={class:"file-info"},Cl={class:"file-icon"},zl={class:"file-details"},xl={class:"file-name"},Ml={class:"file-size"},$l={class:"file-progress"},Sl={key:1,class:"upload-speed"},Fl={key:2,class:"error-message"},Bl={key:0,class:"current-status"},Tl={class:"status-text"},Dl={class:"upload-stats"},El={class:"dialog-footer"},Vl={class:"batch-search-content"},Ul={class:"search-stats"},Pl={key:0,class:"error-text"},Ll={key:0,class:"keywords-preview"},ql={class:"keywords-list"},jl={class:"dialog-footer"},Al=pt({__name:"FileManager",setup(Ol){const Ve=ct(),k=_([]),E=_(!1),P=_(!1),N=_(""),se=_(""),F=_(1),q=_(20),B=_(0),h=_([]),V=_(!1),U=_("table"),R=_(!0),j=_(!1),K=_(""),w=_([]),b=_(!1),v=_([]),Y=_(""),A=_(),G=S(()=>v.value.filter(l=>l.status==="success").length),O=S(()=>v.value.length),le=S(()=>O.value===0?0:v.value.reduce((e,o)=>e+(o.progress||0),0)/O.value),Ue=S(()=>{if(G.value===O.value&&O.value>0)return"success";if(v.value.some(l=>l.status==="error"))return"exception"}),ae=_("0 KB/s"),oe=_("--"),X=_(0),Pe=_(0),ne=S(()=>"/api/v1/files/upload"),ie=S(()=>({Authorization:`Bearer ${Ve.token}`})),Le=S(()=>k.value.reduce((l,e)=>l+(e.size||0),0)),qe=S(()=>{const l=new Date(Date.now()-864e5);return k.value.filter(e=>new Date(e.created_at)>l).length}),je=S(()=>{const l=B.value,e=[10,20,50];if(l<=50)return e;if(l<=200)return[...e,100];if(l<=500)return[...e,100,200,500];if(l<=1e3)return[...e,100,200,500,1e3,l];if(l<=5e3)return[...e,100,200,500,1e3,2e3,Math.ceil(l/2),l];{const o=Math.ceil(l/4),r=Math.ceil(l/2);return[...e,100,200,500,1e3,2e3,o,r,l]}}),ue=l=>{const e=l.lastIndexOf(".");return e>0?l.substring(e+1).toLowerCase():""},re=l=>{const e=(l==null?void 0:l.toLowerCase())||"";return["jpg","jpeg","png","gif","webp","svg"].includes(e)?Me:["pdf","doc","docx","txt","md"].includes(e)?D:["mp4","avi","mov","mkv","webm"].includes(e)?$e:["mp3","wav","flac","aac","ogg"].includes(e)?D:["zip","rar","7z","tar","gz"].includes(e)?D:["js","ts","vue","html","css","json","xml"].includes(e)?D:D},$=async(l=!1)=>{try{l?P.value=!0:(E.value=!0,k.value=[]);const e={page:F.value,limit:q.value,search:N.value},o=await L.files.list(e),r=o.data.data.files;l?k.value=[...k.value,...r]:k.value=r,B.value=o.data.data.pagination.total,R.value=k.value.length<B.value}catch(e){console.error("加载文件列表失败:",e),m.error("加载文件列表失败")}finally{E.value=!1,P.value=!1}},Ae=async()=>{P.value||!R.value||(F.value++,await $(!0))},Oe=()=>{F.value=1,$()},He=l=>{const e=window.scrollY;q.value=l,F.value=1,$().then(()=>{ye(()=>{window.scrollTo(0,e)})})},Ne=l=>{const e=window.scrollY;F.value=l,$(!1).then(()=>{ye(()=>{window.scrollTo(0,e)})})},Re=l=>{h.value=l},de=async l=>{try{const e=await L.files.download(l.id);vt(e.data,l.original_name),m.success("文件下载成功")}catch(e){console.error("下载文件失败:",e),m.error("下载文件失败")}},ce=async l=>{try{await I.confirm(`确定要删除文件 "${l.original_name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await L.files.delete(l.id),m.success("文件删除成功"),$()}catch(e){e!=="cancel"&&(console.error("删除文件失败:",e),m.error("删除文件失败"))}},ve=async()=>{try{const{value:l}=await I.prompt("请输入文件夹名称","新建文件夹",{confirmButtonText:"确定",cancelButtonText:"取消"});l&&(await L.directories.create({name:l}),m.success("文件夹创建成功"),$())}catch(l){l!=="cancel"&&(console.error("创建文件夹失败:",l),m.error("创建文件夹失败"))}},Ke=l=>l.size>104857600?(m.error("文件大小不能超过100MB"),!1):!0,Ye=l=>new Date(l).toLocaleString(),Je=l=>{const e=h.value.findIndex(o=>o.id===l.id);e>-1?h.value.splice(e,1):h.value.push(l)},Qe=async()=>{if(h.value.length!==0)try{await I.confirm(`确定要删除选中的 ${h.value.length} 个文件吗？此操作不可撤销。`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const l=h.value.map(e=>L.files.delete(e.id));await Promise.all(l),m.success(`成功删除 ${h.value.length} 个文件`),h.value=[],$()}catch(l){l!=="cancel"&&(console.error("批量删除失败:",l),m.error("批量删除失败"))}},Ge=(l,e)=>{v.value=e.map(o=>({name:o.name,size:o.size,status:"ready",progress:0,speed:"",errorMessage:"",file:o.raw}))},Xe=()=>{v.value=[],A.value&&A.value.clearFiles()},We=async()=>{if(v.value.length!==0){b.value=!0,X.value=Date.now(),Pe.value=0;try{for(let o=0;o<v.value.length;o++){const r=v.value[o];Y.value=r.name,r.status="uploading",await Ze(r,o)}const l=v.value.filter(o=>o.status==="success").length,e=v.value.filter(o=>o.status==="error").length;l>0&&(m.success(`成功上传 ${l} 个文件${e>0?`，${e} 个文件失败`:""}`),$()),e===v.value.length&&m.error("所有文件上传失败")}catch(l){console.error("上传过程中出错:",l),m.error("上传过程中出现错误")}finally{b.value=!1,Y.value=""}}},Ze=(l,e)=>new Promise((o,r)=>{const x=new FormData;x.append("files",l.file);const g=new XMLHttpRequest;g.upload.addEventListener("progress",C=>{if(C.lengthComputable){const H=Math.round(C.loaded/C.total*100);l.progress=H;const z=(Date.now()-X.value)/1e3,T=C.loaded/z;l.speed=_e(T),Ie()}}),g.addEventListener("load",()=>{if(g.status===200)try{const C=JSON.parse(g.responseText);C.success?(l.status="success",l.progress=100):(l.status="error",l.errorMessage=C.message||"上传失败")}catch{l.status="error",l.errorMessage="响应解析失败"}else l.status="error",l.errorMessage=`HTTP ${g.status}`;o()}),g.addEventListener("error",()=>{l.status="error",l.errorMessage="网络错误",o()}),g.open("POST",ne.value),g.setRequestHeader("Authorization",ie.value.Authorization),g.send(x)}),Ie=()=>{v.value.reduce((x,g)=>x+g.progress,0)/v.value.length;const e=(Date.now()-X.value)/1e3,o=v.value.filter(x=>x.status==="success").length,r=v.value.length-o;if(e>0&&o>0){const g=e/o*r;oe.value=et(g),v.value.reduce((z,T)=>z+T.size,0);const H=v.value.reduce((z,T)=>z+T.size*T.progress/100,0)/e;ae.value=_e(H)}},_e=l=>l<1024?`${Math.round(l)} B/s`:l<1024*1024?`${Math.round(l/1024)} KB/s`:`${Math.round(l/(1024*1024))} MB/s`,et=l=>l<60?`${Math.round(l)}秒`:l<3600?`${Math.round(l/60)}分钟`:`${Math.round(l/3600)}小时`,tt=()=>{b.value=!1,Y.value="",v.value.forEach(l=>{l.status==="uploading"&&(l.status="error",l.errorMessage="用户取消")}),m.info("上传已取消")},st=()=>{V.value=!1,v.value=[],A.value&&A.value.clearFiles()},lt=(l,e,o)=>{},at=(l,e,o)=>{},ot=(l,e,o)=>{},nt=()=>{const l=K.value.split(`
`).map(e=>e.trim()).filter(e=>e.length>0);w.value=l},it=()=>{K.value="",w.value=[]},ut=async()=>{if(w.value.length===0){m.warning("请输入搜索关键词");return}if(w.value.length>100){m.error("关键词数量不能超过100个");return}try{E.value=!0,j.value=!1;const l=await L.files.batchSearch({keywords:w.value,page:1,limit:q.value});k.value=l.data.data.files,B.value=l.data.data.pagination.total,F.value=1,R.value=k.value.length<B.value,N.value=`批量搜索(${w.value.length}个关键词)`,m.success(`找到 ${k.value.length} 个匹配的文件`)}catch(l){console.error("批量搜索失败:",l),m.error("批量搜索失败，请重试")}finally{E.value=!1}};return ft(()=>{$()}),(l,e)=>{const o=gt,r=mt,x=ht,g=At,C=yt,H=kt,z=Ot,T=wt,rt=bt,dt=Ht,pe=Nt,fe=Ct,ge=Rt,me=zt;return u(),p("div",Kt,[t("div",Yt,[e[23]||(e[23]=t("div",{class:"banner-background"},[t("div",{class:"file-pattern"})],-1)),t("div",Jt,[t("div",Qt,[t("div",Gt,[t("div",Xt,[s(o,null,{default:a(()=>[s(i(Ce))]),_:1})]),e[19]||(e[19]=t("div",{class:"title-content"},[t("h1",{class:"banner-title"},"文件管理中心"),t("div",{class:"title-decoration"})],-1))]),e[22]||(e[22]=t("p",{class:"banner-subtitle"}," 高效管理您的文件，支持批量操作、智能搜索和多种视图模式 ",-1)),t("div",Wt,[s(r,{type:"primary",size:"large",onClick:e[0]||(e[0]=n=>V.value=!0)},{default:a(()=>[s(o,null,{default:a(()=>[s(i(ze))]),_:1}),e[20]||(e[20]=c(" 上传文件 ",-1))]),_:1,__:[20]}),s(r,{size:"large",onClick:ve},{default:a(()=>[s(o,null,{default:a(()=>[s(i(xe))]),_:1}),e[21]||(e[21]=c(" 新建文件夹 ",-1))]),_:1,__:[21]})])]),t("div",Zt,[t("div",It,[t("div",es,[s(o,null,{default:a(()=>[s(i(D))]),_:1})]),t("div",ts,[s(o,null,{default:a(()=>[s(i(Me))]),_:1})]),t("div",ss,[s(o,null,{default:a(()=>[s(i($e))]),_:1})])])])])]),t("div",ls,[t("h2",as,[s(o,null,{default:a(()=>[s(i(xt))]),_:1}),e[24]||(e[24]=c(" 文件统计 ",-1))]),t("div",os,[t("div",ns,[e[27]||(e[27]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",is,[s(o,null,{default:a(()=>[s(i(D))]),_:1})]),t("div",us,[t("div",rs,d(k.value.length),1),e[26]||(e[26]=t("div",{class:"stats-label"},"文件总数",-1)),t("div",ds,[s(o,null,{default:a(()=>[s(i(Mt))]),_:1}),e[25]||(e[25]=t("span",null,"当前显示文件",-1))])])]),h.value.length>0?(u(),p("div",cs,[e[30]||(e[30]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",vs,[s(o,null,{default:a(()=>[s(i(Se))]),_:1})]),t("div",_s,[t("div",ps,d(h.value.length),1),e[29]||(e[29]=t("div",{class:"stats-label"},"已选择",-1)),t("div",fs,[s(o,null,{default:a(()=>[s(i(ee))]),_:1}),e[28]||(e[28]=t("span",null,"可进行批量操作",-1))])])])):f("",!0),t("div",gs,[e[33]||(e[33]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",ms,[s(o,null,{default:a(()=>[s(i($t))]),_:1})]),t("div",hs,[t("div",ys,d(i(J)(Le.value)),1),e[32]||(e[32]=t("div",{class:"stats-label"},"总大小",-1)),t("div",ks,[s(o,null,{default:a(()=>[s(i(St))]),_:1}),e[31]||(e[31]=t("span",null,"存储使用情况",-1))])])]),t("div",ws,[e[36]||(e[36]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",bs,[s(o,null,{default:a(()=>[s(i(Ft))]),_:1})]),t("div",Cs,[t("div",zs,d(qe.value),1),e[35]||(e[35]=t("div",{class:"stats-label"},"最近文件",-1)),t("div",xs,[s(o,null,{default:a(()=>[s(i(Bt))]),_:1}),e[34]||(e[34]=t("span",null,"24小时内上传",-1))])])])])]),t("div",Ms,[t("h2",$s,[s(o,null,{default:a(()=>[s(i(Tt))]),_:1}),e[37]||(e[37]=c(" 快捷操作 ",-1))]),t("div",Ss,[t("div",{class:"quick-action-card",onClick:e[1]||(e[1]=n=>V.value=!0)},[t("div",Fs,[s(o,null,{default:a(()=>[s(i(ze))]),_:1})]),e[38]||(e[38]=t("div",{class:"action-content"},[t("h3",null,"上传文件"),t("p",null,"支持拖拽上传，多文件同时上传")],-1)),t("div",Bs,[s(o,null,{default:a(()=>[s(i(Q))]),_:1})])]),t("div",{class:"quick-action-card",onClick:ve},[t("div",Ts,[s(o,null,{default:a(()=>[s(i(xe))]),_:1})]),e[39]||(e[39]=t("div",{class:"action-content"},[t("h3",null,"新建文件夹"),t("p",null,"创建新的文件夹来组织您的文件")],-1)),t("div",Ds,[s(o,null,{default:a(()=>[s(i(Q))]),_:1})])]),t("div",{class:"quick-action-card",onClick:e[2]||(e[2]=n=>U.value=U.value==="table"?"grid":"table")},[t("div",Es,[s(o,null,{default:a(()=>[s(i(Fe))]),_:1})]),e[40]||(e[40]=t("div",{class:"action-content"},[t("h3",null,"切换视图"),t("p",null,"在列表视图和网格视图之间切换")],-1)),t("div",Vs,[s(o,null,{default:a(()=>[s(i(Q))]),_:1})])]),h.value.length>0?(u(),p("div",{key:0,class:"quick-action-card",onClick:e[3]||(e[3]=n=>l.$router.push("/batch-rename"))},[t("div",Us,[s(o,null,{default:a(()=>[s(i(Be))]),_:1})]),t("div",Ps,[e[41]||(e[41]=t("h3",null,"批量重命名",-1)),t("p",null,"对选中的 "+d(h.value.length)+" 个文件进行重命名",1)]),t("div",Ls,[s(o,null,{default:a(()=>[s(i(Q))]),_:1})])])):f("",!0)])]),t("div",qs,[t("div",js,[t("h2",As,[s(o,null,{default:a(()=>[s(i(Ce))]),_:1}),e[42]||(e[42]=c(" 文件浏览器 ",-1))]),t("div",Os,[t("div",Hs,[s(x,{modelValue:N.value,"onUpdate:modelValue":e[4]||(e[4]=n=>N.value=n),placeholder:"搜索文件...",onInput:Oe,style:{width:"300px"}},{prefix:a(()=>[s(o,null,{default:a(()=>[s(i(Dt))]),_:1})]),_:1},8,["modelValue"]),s(r,{onClick:e[5]||(e[5]=n=>j.value=!0),type:"primary"},{default:a(()=>[s(o,null,{default:a(()=>[s(i(Et))]),_:1}),e[43]||(e[43]=c(" 批量搜索 ",-1))]),_:1,__:[43]})]),s(C,{modelValue:se.value,"onUpdate:modelValue":e[6]||(e[6]=n=>se.value=n),placeholder:"文件类型",style:{width:"120px"}},{default:a(()=>[s(g,{label:"全部",value:""}),s(g,{label:"图片",value:"jpg,jpeg,png,gif"}),s(g,{label:"文档",value:"pdf,doc,docx,txt"}),s(g,{label:"视频",value:"mp4,avi,mov"})]),_:1},8,["modelValue"]),s(H,null,{default:a(()=>[s(r,{type:U.value==="table"?"primary":"",onClick:e[7]||(e[7]=n=>U.value="table")},{default:a(()=>[s(o,null,{default:a(()=>[s(i(Vt))]),_:1})]),_:1},8,["type"]),s(r,{type:U.value==="grid"?"primary":"",onClick:e[8]||(e[8]=n=>U.value="grid")},{default:a(()=>[s(o,null,{default:a(()=>[s(i(Fe))]),_:1})]),_:1},8,["type"])]),_:1})])]),t("div",Ns,[h.value.length>0?(u(),p("div",Rs,[t("div",Ks,[s(o,null,{default:a(()=>[s(i(Se))]),_:1}),c(" 已选择 "+d(h.value.length)+" 个文件 ",1)]),t("div",Ys,[s(r,{onClick:e[9]||(e[9]=n=>l.$router.push("/batch-rename")),type:"primary"},{default:a(()=>[s(o,null,{default:a(()=>[s(i(Be))]),_:1}),e[44]||(e[44]=c(" 批量重命名 ",-1))]),_:1,__:[44]}),s(r,{onClick:Qe,type:"danger"},{default:a(()=>[s(o,null,{default:a(()=>[s(i(te))]),_:1}),e[45]||(e[45]=c(" 批量删除 ",-1))]),_:1,__:[45]}),s(r,{onClick:e[10]||(e[10]=n=>h.value=[])},{default:a(()=>[s(o,null,{default:a(()=>[s(i(Te))]),_:1}),e[46]||(e[46]=c(" 取消选择 ",-1))]),_:1,__:[46]})])])):f("",!0),U.value==="table"?(u(),p("div",Js,[he((u(),y(T,{data:k.value,onSelectionChange:Re,"empty-text":"暂无文件数据"},{default:a(()=>[s(z,{type:"selection",width:"55"}),s(z,{label:"文件名","min-width":"200"},{default:a(({row:n})=>[t("div",Qs,[s(o,{size:"20"},{default:a(()=>[(u(),y(ke(re(ue(n.original_name)))))]),_:2},1024),t("span",null,d(n.original_name),1)])]),_:1}),s(z,{prop:"size",label:"大小",width:"100"},{default:a(({row:n})=>[c(d(i(J)(n.size)),1)]),_:1}),s(z,{prop:"created_at",label:"上传时间",width:"180"},{default:a(({row:n})=>[c(d(Ye(n.created_at)),1)]),_:1}),s(z,{label:"操作",width:"200"},{default:a(({row:n})=>[s(r,{size:"small",onClick:M=>de(n)},{default:a(()=>[s(o,null,{default:a(()=>[s(i(De))]),_:1}),e[47]||(e[47]=c(" 下载 ",-1))]),_:2,__:[47]},1032,["onClick"]),s(r,{size:"small",type:"danger",onClick:M=>ce(n)},{default:a(()=>[s(o,null,{default:a(()=>[s(i(te))]),_:1}),e[48]||(e[48]=c(" 删除 ",-1))]),_:2,__:[48]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[me,E.value]])])):(u(),p("div",Gs,[k.value.length===0&&!E.value?(u(),p("div",Xs,[t("div",Ws,[t("div",Zs,[s(o,null,{default:a(()=>[s(i(Ut))]),_:1})]),t("div",Is,[e[50]||(e[50]=t("h3",null,"还没有文件",-1)),e[51]||(e[51]=t("p",null,"开始上传您的第一个文件",-1)),s(r,{type:"primary",onClick:e[11]||(e[11]=n=>V.value=!0)},{default:a(()=>e[49]||(e[49]=[c(" 立即上传 ",-1)])),_:1,__:[49]})])])])):he((u(),p("div",el,[(u(!0),p(W,null,Z(k.value,n=>(u(),p("div",{key:n.id,class:we(["file-item",{selected:h.value.some(M=>M.id===n.id)}]),onClick:M=>Je(n)},[t("div",sl,[s(o,{size:"48"},{default:a(()=>[(u(),y(ke(re(ue(n.original_name)))))]),_:2},1024)]),t("div",ll,d(n.original_name),1),t("div",al,[t("span",ol,d(i(J)(n.size)),1)]),t("div",nl,[s(r,{size:"small",onClick:be(M=>de(n),["stop"]),type:"primary"},{default:a(()=>[s(o,null,{default:a(()=>[s(i(De))]),_:1})]),_:2},1032,["onClick"]),s(r,{size:"small",onClick:be(M=>ce(n),["stop"]),type:"danger"},{default:a(()=>[s(o,null,{default:a(()=>[s(i(te))]),_:1})]),_:2},1032,["onClick"])])],10,tl))),128))])),[[me,E.value]])])),B.value>0?(u(),p("div",il,[s(rt,{"current-page":F.value,"onUpdate:currentPage":e[12]||(e[12]=n=>F.value=n),"page-size":q.value,"onUpdate:pageSize":e[13]||(e[13]=n=>q.value=n),total:B.value,"page-sizes":je.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:He,onCurrentChange:Ne,background:""},null,8,["current-page","page-size","total","page-sizes"]),R.value?(u(),p("div",ul,[s(r,{onClick:Ae,loading:P.value,type:"primary",size:"large",class:"load-more-btn"},{default:a(()=>[P.value?f("",!0):(u(),y(o,{key:0},{default:a(()=>[s(i(Pt))]),_:1})),c(" "+d(P.value?"加载中...":"加载更多文件"),1)]),_:1},8,["loading"]),t("div",rl," 已显示 "+d(k.value.length)+" / "+d(B.value)+" 个文件 ",1)])):f("",!0)])):f("",!0)])]),s(fe,{modelValue:V.value,"onUpdate:modelValue":e[15]||(e[15]=n=>V.value=n),title:"上传文件",width:"700px","close-on-click-modal":!1,"close-on-press-escape":!b.value,"show-close":!b.value},{footer:a(()=>[t("div",El,[!b.value&&v.value.length===0?(u(),y(r,{key:0,onClick:e[14]||(e[14]=n=>V.value=!1)},{default:a(()=>e[57]||(e[57]=[c(" 取消 ",-1)])),_:1,__:[57]})):f("",!0),!b.value&&v.value.length>0?(u(),y(r,{key:1,onClick:Xe},{default:a(()=>e[58]||(e[58]=[c(" 清空列表 ",-1)])),_:1,__:[58]})):f("",!0),!b.value&&v.value.length>0?(u(),y(r,{key:2,type:"primary",onClick:We},{default:a(()=>[c(" 开始上传 ("+d(v.value.length)+" 个文件) ",1)]),_:1})):f("",!0),b.value?(u(),y(r,{key:3,type:"danger",onClick:tt},{default:a(()=>e[59]||(e[59]=[c(" 取消上传 ",-1)])),_:1,__:[59]})):f("",!0),!b.value&&G.value>0?(u(),y(r,{key:4,type:"success",onClick:st},{default:a(()=>e[60]||(e[60]=[c(" 完成 ",-1)])),_:1,__:[60]})):f("",!0)])]),default:a(()=>[t("div",dl,[b.value?f("",!0):(u(),p("div",cl,[s(dt,{ref_key:"uploadRef",ref:A,action:ne.value,headers:ie.value,"on-success":lt,"on-error":at,"before-upload":Ke,"on-progress":ot,"on-change":Ge,name:"files",multiple:"",drag:"",class:"modern-upload","auto-upload":!1},{tip:a(()=>[t("div",_l,[t("div",pl,[s(o,null,{default:a(()=>[s(i(Lt))]),_:1}),e[53]||(e[53]=t("span",null,"支持多文件同时上传",-1))]),t("div",fl,[s(o,null,{default:a(()=>[s(i(qt))]),_:1}),e[54]||(e[54]=t("span",null,"单个文件不超过100MB",-1))]),t("div",gl,[s(o,null,{default:a(()=>[s(i(ee))]),_:1}),e[55]||(e[55]=t("span",null,"支持常见文件格式",-1))])])]),default:a(()=>[t("div",vl,[s(o,{class:"upload-icon"},{default:a(()=>[s(i(jt))]),_:1}),e[52]||(e[52]=t("div",{class:"upload-text"},[t("div",{class:"upload-title"},"拖拽文件到此处上传"),t("div",{class:"upload-subtitle"},[c("或 "),t("em",null,"点击选择文件")])],-1))])]),_:1},8,["action","headers"])])),v.value.length>0?(u(),p("div",ml,[t("div",hl,[e[56]||(e[56]=t("h4",null,"上传进度",-1)),t("div",yl,[t("div",kl,[t("span",null,"总进度: "+d(Math.round(le.value))+"%",1),t("span",null,d(G.value)+"/"+d(O.value)+" 个文件",1)]),s(pe,{percentage:Math.round(le.value),status:Ue.value,"stroke-width":8},null,8,["percentage","status"])])]),t("div",wl,[(u(!0),p(W,null,Z(v.value,(n,M)=>(u(),p("div",{key:M,class:we(["file-progress-item",{uploading:n.status==="uploading",success:n.status==="success",error:n.status==="error"}])},[t("div",bl,[t("div",Cl,[n.status==="success"?(u(),y(o,{key:0},{default:a(()=>[s(i(ee))]),_:1})):n.status==="error"?(u(),y(o,{key:1},{default:a(()=>[s(i(Te))]),_:1})):n.status==="uploading"?(u(),y(o,{key:2},{default:a(()=>[s(i(Ee))]),_:1})):(u(),y(o,{key:3},{default:a(()=>[s(i(D))]),_:1}))]),t("div",zl,[t("div",xl,d(n.name),1),t("div",Ml,d(i(J)(n.size)),1)])]),t("div",$l,[n.status==="uploading"||n.status==="success"?(u(),y(pe,{key:0,percentage:n.progress,status:n.status==="success"?"success":void 0,"stroke-width":4,"show-text":!1},null,8,["percentage","status"])):f("",!0),n.status==="uploading"?(u(),p("div",Sl,d(n.speed||"计算中..."),1)):f("",!0),n.status==="error"?(u(),p("div",Fl," 上传失败: "+d(n.errorMessage),1)):f("",!0)])],2))),128))]),b.value?(u(),p("div",Bl,[t("div",Tl,[s(o,{class:"rotating"},{default:a(()=>[s(i(Ee))]),_:1}),c(" 正在上传: "+d(Y.value),1)]),t("div",Dl,[t("span",null,"上传速度: "+d(ae.value),1),t("span",null,"剩余时间: "+d(oe.value),1)])])):f("",!0)])):f("",!0)])]),_:1},8,["modelValue","close-on-press-escape","show-close"]),s(fe,{modelValue:j.value,"onUpdate:modelValue":e[18]||(e[18]=n=>j.value=n),title:"批量搜索文件",width:"600px","close-on-click-modal":!1},{footer:a(()=>[t("div",jl,[s(r,{onClick:e[17]||(e[17]=n=>j.value=!1)},{default:a(()=>e[63]||(e[63]=[c("取消",-1)])),_:1,__:[63]}),s(r,{onClick:it},{default:a(()=>e[64]||(e[64]=[c("清空",-1)])),_:1,__:[64]}),s(r,{type:"primary",onClick:ut,disabled:w.value.length===0||w.value.length>100},{default:a(()=>[c(" 搜索 ("+d(w.value.length)+" 个关键词) ",1)]),_:1},8,["disabled"])])]),default:a(()=>[t("div",Vl,[e[62]||(e[62]=t("div",{class:"search-instructions"},[t("h4",null,"使用说明"),t("ul",null,[t("li",null,"每行输入一个搜索关键词"),t("li",null,"支持粘贴多个关键词（自动按行分割）"),t("li",null,"将搜索所有包含任意关键词的文件"),t("li",null,"最多支持100个关键词")])],-1)),s(x,{modelValue:K.value,"onUpdate:modelValue":e[16]||(e[16]=n=>K.value=n),type:"textarea",rows:10,placeholder:`请输入搜索关键词，每行一个
例如：
9200190392641700162321
document.pdf
image.jpg`,onInput:nt},null,8,["modelValue"]),t("div",Ul,[t("span",null,"关键词数量: "+d(w.value.length),1),w.value.length>100?(u(),p("span",Pl," 超出限制！最多支持100个关键词 ")):f("",!0)]),w.value.length>0?(u(),p("div",Ll,[e[61]||(e[61]=t("h5",null,"关键词预览:",-1)),t("div",ql,[(u(!0),p(W,null,Z(w.value.slice(0,10),(n,M)=>(u(),y(ge,{key:M,size:"small",class:"keyword-tag"},{default:a(()=>[c(d(n),1)]),_:2},1024))),128)),w.value.length>10?(u(),y(ge,{key:0,size:"small",type:"info"},{default:a(()=>[c(" +"+d(w.value.length-10)+" 更多... ",1)]),_:1})):f("",!0)])])):f("",!0)])]),_:1},8,["modelValue"])])}}});const Xl=_t(Al,[["__scopeId","data-v-f73da571"]]);export{Xl as default};
