import{u as J,a as C,f as K,_ as W}from"./index-4cbb9fd4.js";/* empty css               */import{a as X,q as Z,r as tt,t as st,v as ot}from"./elementPlus-b5b9032b.js";import{x as nt,r as g,k as et,z as u,A as t,O as r,u as b,Q as o,I as e,aD as at,M as c,P as lt,a4 as it,al as i,y as d,H as v}from"./vendor-a56c79b3.js";const dt={class:"dashboard-page"},rt={class:"welcome-banner"},ct={class:"welcome-content"},ut={class:"welcome-text"},_t={class:"welcome-title"},vt={class:"greeting"},pt={class:"username"},ft={class:"welcome-actions"},mt={class:"stats-section"},yt={class:"section-title"},gt={class:"stats-grid"},kt={class:"stats-icon"},wt={class:"stats-content"},Dt={class:"stats-value"},$t={class:"stats-trend"},ht={class:"stats-icon"},Ct={class:"stats-content"},bt={class:"stats-value"},St={class:"stats-trend"},Et={class:"stats-card warning"},Mt={class:"stats-icon"},qt={class:"stats-content"},zt={class:"stats-value"},At={class:"stats-trend"},Bt={class:"stats-icon"},Ft={class:"stats-content"},Lt={class:"stats-value"},Vt={class:"stats-trend"},Ot={class:"quick-actions-section"},Rt={class:"section-title"},Tt={class:"quick-actions-grid"},Ht={class:"action-icon"},It={class:"action-arrow"},Nt={class:"action-icon"},Yt={class:"action-arrow"},Pt={class:"action-icon"},Ut={class:"action-arrow"},jt={class:"action-icon"},xt={class:"action-arrow"},Gt={class:"recent-activity-section"},Qt={class:"section-header"},Jt={class:"section-title"},Kt={class:"activity-container"},Wt={key:0,class:"loading-state"},Xt={key:1,class:"empty-state"},Zt={class:"empty-illustration"},ts={class:"empty-icon"},ss={class:"empty-content"},os={key:2,class:"activity-list"},ns={class:"activity-icon"},es={class:"activity-content"},as={class:"activity-title"},ls={class:"activity-description"},is={class:"activity-time"},ds=nt({__name:"Dashboard",setup(rs){const S=J().user,p=g({total_files:0,file_count:0,directory_count:0,total_size:0,storage_used:0,total_operations:0,successful_operations:0,failed_operations:0}),f=g([]),y=g(!1),E=async()=>{try{const n=await C.system.getStats();p.value=n.data.data}catch(n){console.error("加载统计信息失败:",n)}},M=async()=>{try{y.value=!0;const n=await C.logs.list({limit:10});f.value=n.data.data.logs}catch(n){console.error("加载最近活动失败:",n)}finally{y.value=!1}},q=n=>({upload:"上传",download:"下载",delete:"删除",rename:"重命名"})[n]||n,z=n=>tt(n).format("YYYY-MM-DD HH:mm:ss"),A=()=>{const n=new Date().getHours();return n<6?"夜深了":n<9?"早上好":n<12?"上午好":n<14?"中午好":n<18?"下午好":n<22?"晚上好":"夜深了"};return et(()=>{E(),M()}),(n,s)=>{var h;const k=i("Upload"),a=X,m=Z,w=i("Edit"),B=i("DataAnalysis"),F=i("Document"),D=i("TrendCharts"),L=i("Folder"),V=i("DataBoard"),O=i("Monitor"),$=i("Operation"),R=i("View"),T=i("Lightning"),H=i("FolderOpened"),_=i("ArrowRight"),I=i("EditPen"),N=i("DocumentCopy"),Y=i("Setting"),P=i("Clock"),U=st,j=i("DocumentRemove"),x=i("Download"),G=i("Delete"),Q=ot;return d(),u("div",dt,[t("div",rt,[t("div",ct,[t("div",ut,[t("h1",_t,[t("span",vt,r(A()),1),t("span",pt,r(((h=b(S))==null?void 0:h.username)||"用户"),1),s[11]||(s[11]=t("span",{class:"wave"},"👋",-1))]),s[14]||(s[14]=t("p",{class:"welcome-subtitle"}," 欢迎回到文件管理工具集，让我们开始高效的文件管理之旅 ",-1)),t("div",ft,[o(m,{type:"primary",size:"large",onClick:s[0]||(s[0]=l=>n.$router.push("/file-manager"))},{default:e(()=>[o(a,null,{default:e(()=>[o(k)]),_:1}),s[12]||(s[12]=c(" 上传文件 ",-1))]),_:1,__:[12]}),o(m,{size:"large",onClick:s[1]||(s[1]=l=>n.$router.push("/batch-rename"))},{default:e(()=>[o(a,null,{default:e(()=>[o(w)]),_:1}),s[13]||(s[13]=c(" 批量重命名 ",-1))]),_:1,__:[13]})])]),s[15]||(s[15]=at('<div class="welcome-illustration" data-v-f4677578><div class="floating-elements" data-v-f4677578><div class="floating-file file-1" data-v-f4677578>📄</div><div class="floating-file file-2" data-v-f4677578>📁</div><div class="floating-file file-3" data-v-f4677578>🖼️</div><div class="floating-file file-4" data-v-f4677578>📊</div></div></div>',1))])]),t("div",mt,[t("h2",yt,[o(a,null,{default:e(()=>[o(B)]),_:1}),s[16]||(s[16]=c(" 数据概览 ",-1))]),t("div",gt,[t("div",{class:"stats-card primary",onClick:s[2]||(s[2]=l=>n.$router.push("/file-manager"))},[s[19]||(s[19]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",kt,[o(a,null,{default:e(()=>[o(F)]),_:1})]),t("div",wt,[t("div",Dt,r(p.value.file_count),1),s[18]||(s[18]=t("div",{class:"stats-label"},"文件总数",-1)),t("div",$t,[o(a,null,{default:e(()=>[o(D)]),_:1}),s[17]||(s[17]=t("span",null,"点击查看详情",-1))])])]),t("div",{class:"stats-card success",onClick:s[3]||(s[3]=l=>n.$router.push("/file-manager"))},[s[22]||(s[22]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",ht,[o(a,null,{default:e(()=>[o(L)]),_:1})]),t("div",Ct,[t("div",bt,r(p.value.directory_count),1),s[21]||(s[21]=t("div",{class:"stats-label"},"目录总数",-1)),t("div",St,[o(a,null,{default:e(()=>[o(D)]),_:1}),s[20]||(s[20]=t("span",null,"点击查看详情",-1))])])]),t("div",Et,[s[25]||(s[25]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",Mt,[o(a,null,{default:e(()=>[o(V)]),_:1})]),t("div",qt,[t("div",zt,r(b(K)(p.value.total_size)),1),s[24]||(s[24]=t("div",{class:"stats-label"},"存储空间",-1)),t("div",At,[o(a,null,{default:e(()=>[o(O)]),_:1}),s[23]||(s[23]=t("span",null,"存储使用情况",-1))])])]),t("div",{class:"stats-card info",onClick:s[4]||(s[4]=l=>n.$router.push("/operation-logs"))},[s[28]||(s[28]=t("div",{class:"stats-background"},[t("div",{class:"stats-pattern"})],-1)),t("div",Bt,[o(a,null,{default:e(()=>[o($)]),_:1})]),t("div",Ft,[t("div",Lt,r(f.value.length),1),s[27]||(s[27]=t("div",{class:"stats-label"},"最近操作",-1)),t("div",Vt,[o(a,null,{default:e(()=>[o(R)]),_:1}),s[26]||(s[26]=t("span",null,"查看操作日志",-1))])])])])]),t("div",Ot,[t("h2",Rt,[o(a,null,{default:e(()=>[o(T)]),_:1}),s[29]||(s[29]=c(" 快捷操作 ",-1))]),t("div",Tt,[t("div",{class:"quick-action-card",onClick:s[5]||(s[5]=l=>n.$router.push("/file-manager"))},[t("div",Ht,[o(a,null,{default:e(()=>[o(H)]),_:1})]),s[30]||(s[30]=t("div",{class:"action-content"},[t("h3",null,"文件管理"),t("p",null,"浏览、上传、下载和管理您的文件")],-1)),t("div",It,[o(a,null,{default:e(()=>[o(_)]),_:1})])]),t("div",{class:"quick-action-card",onClick:s[6]||(s[6]=l=>n.$router.push("/batch-rename"))},[t("div",Nt,[o(a,null,{default:e(()=>[o(I)]),_:1})]),s[31]||(s[31]=t("div",{class:"action-content"},[t("h3",null,"批量重命名"),t("p",null,"使用强大的规则批量重命名文件")],-1)),t("div",Yt,[o(a,null,{default:e(()=>[o(_)]),_:1})])]),t("div",{class:"quick-action-card",onClick:s[7]||(s[7]=l=>n.$router.push("/operation-logs"))},[t("div",Pt,[o(a,null,{default:e(()=>[o(N)]),_:1})]),s[32]||(s[32]=t("div",{class:"action-content"},[t("h3",null,"操作日志"),t("p",null,"查看详细的操作历史和统计信息")],-1)),t("div",Ut,[o(a,null,{default:e(()=>[o(_)]),_:1})])]),t("div",{class:"quick-action-card",onClick:s[8]||(s[8]=l=>n.$router.push("/profile"))},[t("div",jt,[o(a,null,{default:e(()=>[o(Y)]),_:1})]),s[33]||(s[33]=t("div",{class:"action-content"},[t("h3",null,"个人设置"),t("p",null,"管理您的账户信息和偏好设置")],-1)),t("div",xt,[o(a,null,{default:e(()=>[o(_)]),_:1})])])])]),t("div",Gt,[t("div",Qt,[t("h2",Jt,[o(a,null,{default:e(()=>[o(P)]),_:1}),s[34]||(s[34]=c(" 最近活动 ",-1))]),o(m,{text:"",onClick:s[9]||(s[9]=l=>n.$router.push("/operation-logs"))},{default:e(()=>[s[35]||(s[35]=c(" 查看全部 ",-1)),o(a,null,{default:e(()=>[o(_)]),_:1})]),_:1,__:[35]})]),t("div",Kt,[y.value?(d(),u("div",Wt,[o(U,{rows:3,animated:""})])):f.value.length===0?(d(),u("div",Xt,[t("div",Zt,[t("div",ts,[o(a,null,{default:e(()=>[o(j)]),_:1})]),t("div",ss,[s[37]||(s[37]=t("h3",null,"还没有操作记录",-1)),s[38]||(s[38]=t("p",null,"开始使用文件管理功能，这里将显示您的操作历史",-1)),o(m,{type:"primary",onClick:s[10]||(s[10]=l=>n.$router.push("/file-manager"))},{default:e(()=>s[36]||(s[36]=[c(" 开始使用 ",-1)])),_:1,__:[36]})])])])):(d(),u("div",os,[(d(!0),u(lt,null,it(f.value.slice(0,5),l=>(d(),u("div",{key:l.id,class:"activity-item"},[t("div",ns,[o(a,null,{default:e(()=>[l.operation_type==="upload"?(d(),v(k,{key:0})):l.operation_type==="download"?(d(),v(x,{key:1})):l.operation_type==="delete"?(d(),v(G,{key:2})):l.operation_type==="rename"?(d(),v(w,{key:3})):(d(),v($,{key:4}))]),_:2},1024)]),t("div",es,[t("div",as,[c(r(q(l.operation_type))+" ",1),o(Q,{type:l.success?"success":"danger",size:"small"},{default:e(()=>[c(r(l.success?"成功":"失败"),1)]),_:2},1032,["type"])]),t("div",ls,r(l.file_path||"未知资源"),1),t("div",is,r(z(l.created_at)),1)])]))),128))]))])])])}}});const fs=W(ds,[["__scopeId","data-v-f4677578"]]);export{fs as default};
