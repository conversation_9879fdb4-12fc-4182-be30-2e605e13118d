import{u as Q,a as X,f as j,_ as G}from"./index-4cbb9fd4.js";/* empty css                     *//* empty css                 */import{a as H,n as J,E as w,o as W,p as Y,q as Z}from"./elementPlus-b5b9032b.js";import{x as $,r as f,X as z,k as ss,z as es,A as s,Q as o,I as t,M as c,O as g,u as os,al as d,y as ts}from"./vendor-a56c79b3.js";const as={class:"profile-page"},ls={class:"page-header"},rs={class:"header-content"},ds={class:"header-text"},ns={class:"page-title"},is={class:"header-avatar"},cs={class:"avatar-container"},_s={class:"avatar-circle"},us={class:"avatar-info"},ps={class:"avatar-name"},ms={class:"profile-content"},fs={class:"profile-card"},vs={class:"card-header"},ws={class:"card-title"},gs={class:"card-content"},ys={class:"form-grid"},hs={class:"form-actions"},bs={class:"profile-card"},Vs={class:"card-header"},xs={class:"card-title"},ks={class:"card-content"},Fs={class:"form-grid"},Es={class:"form-actions"},zs={class:"profile-card"},Cs={class:"card-header"},Ps={class:"card-title"},Us={class:"card-content"},Rs={class:"stats-grid"},Ds={class:"stat-card primary"},Ms={class:"stat-icon"},Bs={class:"stat-content"},Ss={class:"stat-value"},qs={class:"stat-trend"},Is={class:"stat-card success"},As={class:"stat-icon"},Ls={class:"stat-content"},Ns={class:"stat-value"},Ts={class:"stat-trend"},Ks={class:"stat-card warning"},Os={class:"stat-icon"},Qs={class:"stat-content"},Xs={class:"stat-value"},js={class:"stat-trend"},Gs=$({__name:"Profile",setup(Hs){const _=Q(),y=f(),h=f(),b=f(!1),V=f(!1),n=z({username:"",email:""}),r=z({old_password:"",new_password:"",confirm_password:""}),v=f({total_files:0,file_count:0,directory_count:0,total_size:0,storage_used:0,total_operations:0,successful_operations:0,failed_operations:0}),C=(a,e,u)=>{e===""?u(new Error("请再次输入密码")):e!==r.new_password?u(new Error("两次输入密码不一致")):u()},P={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},U={old_password:[{required:!0,message:"请输入当前密码",trigger:"blur"}],new_password:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于 6 个字符",trigger:"blur"}],confirm_password:[{required:!0,validator:C,trigger:"blur"}]},R=()=>{_.user&&(n.username=_.user.username,n.email=_.user.email)},D=async()=>{try{const a=await X.system.getStats();v.value=a.data.data}catch(a){console.error("加载统计信息失败:",a)}},M=async()=>{if(y.value)try{await y.value.validate(),b.value=!0;const a=await _.updateProfile({email:n.email});a.success?w.success("资料更新成功"):w.error(a.message||"更新失败")}catch(a){console.error("更新资料失败:",a)}finally{b.value=!1}},B=async()=>{if(h.value)try{await h.value.validate(),V.value=!0;const a=await _.changePassword({old_password:r.old_password,new_password:r.new_password});a.success?(w.success("密码修改成功"),r.old_password="",r.new_password="",r.confirm_password=""):w.error(a.message||"修改密码失败")}catch(a){console.error("修改密码失败:",a)}finally{V.value=!1}};return ss(()=>{R(),D()}),(a,e)=>{const u=d("User"),l=H,S=d("UserFilled"),q=d("Edit"),x=d("Lock"),p=W,m=Y,I=d("Check"),k=Z,F=J,A=d("Refresh"),L=d("DataAnalysis"),N=d("Document"),E=d("TrendCharts"),T=d("Folder"),K=d("DataBoard"),O=d("Monitor");return ts(),es("div",as,[s("div",ls,[s("div",rs,[s("div",ds,[s("h1",ns,[o(l,null,{default:t(()=>[o(u)]),_:1}),e[5]||(e[5]=c(" 个人设置 ",-1))]),e[6]||(e[6]=s("p",{class:"page-subtitle"},"管理您的账户信息和偏好设置",-1))]),s("div",is,[s("div",cs,[s("div",_s,[o(l,null,{default:t(()=>[o(S)]),_:1})]),s("div",us,[s("div",ps,g(n.username),1),e[7]||(e[7]=s("div",{class:"avatar-role"},"管理员",-1))])])])])]),s("div",ms,[s("div",fs,[s("div",vs,[s("div",ws,[o(l,null,{default:t(()=>[o(q)]),_:1}),e[8]||(e[8]=c(" 基本信息 ",-1))]),e[9]||(e[9]=s("div",{class:"card-description"},"更新您的个人资料信息",-1))]),s("div",gs,[o(F,{model:n,rules:P,ref_key:"profileFormRef",ref:y,"label-position":"top"},{default:t(()=>[s("div",ys,[o(m,{label:"用户名",prop:"username"},{default:t(()=>[o(p,{modelValue:n.username,"onUpdate:modelValue":e[0]||(e[0]=i=>n.username=i),disabled:"",size:"large","prefix-icon":"User"},{suffix:t(()=>[o(l,{class:"disabled-icon"},{default:t(()=>[o(x)]),_:1})]),_:1},8,["modelValue"]),e[10]||(e[10]=s("div",{class:"form-help"},"用户名创建后无法修改",-1))]),_:1,__:[10]}),o(m,{label:"邮箱地址",prop:"email"},{default:t(()=>[o(p,{modelValue:n.email,"onUpdate:modelValue":e[1]||(e[1]=i=>n.email=i),size:"large","prefix-icon":"Message",placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1})]),s("div",hs,[o(k,{type:"primary",size:"large",onClick:M,loading:b.value},{default:t(()=>[o(l,null,{default:t(()=>[o(I)]),_:1}),e[11]||(e[11]=c(" 更新资料 ",-1))]),_:1,__:[11]},8,["loading"])])]),_:1},8,["model"])])]),s("div",bs,[s("div",Vs,[s("div",xs,[o(l,null,{default:t(()=>[o(x)]),_:1}),e[12]||(e[12]=c(" 安全设置 ",-1))]),e[13]||(e[13]=s("div",{class:"card-description"},"修改您的登录密码以保护账户安全",-1))]),s("div",ks,[o(F,{model:r,rules:U,ref_key:"passwordFormRef",ref:h,"label-position":"top"},{default:t(()=>[s("div",Fs,[o(m,{label:"当前密码",prop:"old_password"},{default:t(()=>[o(p,{modelValue:r.old_password,"onUpdate:modelValue":e[2]||(e[2]=i=>r.old_password=i),type:"password","show-password":"",size:"large","prefix-icon":"Lock",placeholder:"请输入当前密码"},null,8,["modelValue"])]),_:1}),o(m,{label:"新密码",prop:"new_password"},{default:t(()=>[o(p,{modelValue:r.new_password,"onUpdate:modelValue":e[3]||(e[3]=i=>r.new_password=i),type:"password","show-password":"",size:"large","prefix-icon":"Key",placeholder:"请输入新密码"},null,8,["modelValue"])]),_:1}),o(m,{label:"确认新密码",prop:"confirm_password"},{default:t(()=>[o(p,{modelValue:r.confirm_password,"onUpdate:modelValue":e[4]||(e[4]=i=>r.confirm_password=i),type:"password","show-password":"",size:"large","prefix-icon":"Key",placeholder:"请再次输入新密码"},null,8,["modelValue"])]),_:1})]),s("div",Es,[o(k,{type:"primary",size:"large",onClick:B,loading:V.value},{default:t(()=>[o(l,null,{default:t(()=>[o(A)]),_:1}),e[14]||(e[14]=c(" 修改密码 ",-1))]),_:1,__:[14]},8,["loading"])])]),_:1},8,["model"])])]),s("div",zs,[s("div",Cs,[s("div",Ps,[o(l,null,{default:t(()=>[o(L)]),_:1}),e[15]||(e[15]=c(" 账户统计 ",-1))]),e[16]||(e[16]=s("div",{class:"card-description"},"查看您的使用情况和存储统计",-1))]),s("div",Us,[s("div",Rs,[s("div",Ds,[e[19]||(e[19]=s("div",{class:"stat-background"},[s("div",{class:"stat-pattern"})],-1)),s("div",Ms,[o(l,null,{default:t(()=>[o(N)]),_:1})]),s("div",Bs,[s("div",Ss,g(v.value.file_count),1),e[18]||(e[18]=s("div",{class:"stat-label"},"文件总数",-1)),s("div",qs,[o(l,null,{default:t(()=>[o(E)]),_:1}),e[17]||(e[17]=s("span",null,"已管理文件",-1))])])]),s("div",Is,[e[22]||(e[22]=s("div",{class:"stat-background"},[s("div",{class:"stat-pattern"})],-1)),s("div",As,[o(l,null,{default:t(()=>[o(T)]),_:1})]),s("div",Ls,[s("div",Ns,g(v.value.directory_count),1),e[21]||(e[21]=s("div",{class:"stat-label"},"目录总数",-1)),s("div",Ts,[o(l,null,{default:t(()=>[o(E)]),_:1}),e[20]||(e[20]=s("span",null,"文件夹数量",-1))])])]),s("div",Ks,[e[25]||(e[25]=s("div",{class:"stat-background"},[s("div",{class:"stat-pattern"})],-1)),s("div",Os,[o(l,null,{default:t(()=>[o(K)]),_:1})]),s("div",Qs,[s("div",Xs,g(os(j)(v.value.total_size)),1),e[24]||(e[24]=s("div",{class:"stat-label"},"存储空间",-1)),s("div",js,[o(l,null,{default:t(()=>[o(O)]),_:1}),e[23]||(e[23]=s("span",null,"已使用空间",-1))])])])])])])])])}}});const se=G(Gs,[["__scopeId","data-v-2dcd79db"]]);export{se as default};
