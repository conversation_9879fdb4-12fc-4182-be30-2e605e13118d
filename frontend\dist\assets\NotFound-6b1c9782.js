import{_ as p}from"./index-4cbb9fd4.js";import{x as m,z as f,A as e,Q as t,I as s,az as v,al as _,y as k,M as d}from"./vendor-a56c79b3.js";import{q as x,a as B}from"./elementPlus-b5b9032b.js";const N={class:"not-found"},g={class:"not-found-content"},y={class:"error-actions"},C=m({__name:"NotFound",setup(F){const n=v(),l=()=>{n.push("/dashboard")},c=()=>{n.go(-1)};return(E,o)=>{const i=_("HomeFilled"),r=B,a=x,u=_("Back");return k(),f("div",N,[e("div",g,[o[2]||(o[2]=e("div",{class:"error-code"},"404",-1)),o[3]||(o[3]=e("div",{class:"error-message"},"页面未找到",-1)),o[4]||(o[4]=e("div",{class:"error-description"}," 抱歉，您访问的页面不存在或已被移除。 ",-1)),e("div",y,[t(a,{type:"primary",onClick:l},{default:s(()=>[t(r,null,{default:s(()=>[t(i)]),_:1}),o[0]||(o[0]=d(" 返回首页 ",-1))]),_:1,__:[0]}),t(a,{onClick:c},{default:s(()=>[t(r,null,{default:s(()=>[t(u)]),_:1}),o[1]||(o[1]=d(" 返回上页 ",-1))]),_:1,__:[1]})])])])}}});const b=p(C,[["__scopeId","data-v-86df7773"]]);export{b as default};
